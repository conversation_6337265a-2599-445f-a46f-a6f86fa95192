{"name": "space/mongo-documents", "description": "Shared MongoDB document definitions for Space microservices", "type": "library", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "doctrine/mongodb-odm-bundle": "^5.3", "doctrine/mongodb-odm": "^2.6", "mongodb/mongodb": "^2.0", "symfony/framework-bundle": "^6.4", "symfony/serializer": "^6.4", "symfony/validator": "^6.4", "jms/serializer-bundle": "^5.5"}, "autoload": {"psr-4": {"Space\\MongoDocuments\\": "src/"}}, "config": {"sort-packages": true}}