<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class ScoreCategory
{
    #[MongoDB\Field(type: 'float')]
    private ?float $percentageOfGood = null;

    #[MongoDB\Field(type: 'float')]
    private ?float $percentageOfAverage = null;

    #[MongoDB\Field(type: 'float')]
    private ?float $percentageOfBad = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $tips = null;

    public function getPercentageOfGood(): ?float
    {
        return $this->percentageOfGood;
    }

    public function setPercentageOfGood(?float $percentageOfGood): self
    {
        $this->percentageOfGood = $percentageOfGood;
        return $this;
    }

    public function getPercentageOfAverage(): ?float
    {
        return $this->percentageOfAverage;
    }

    public function setPercentageOfAverage(?float $percentageOfAverage): self
    {
        $this->percentageOfAverage = $percentageOfAverage;
        return $this;
    }

    public function getPercentageOfBad(): ?float
    {
        return $this->percentageOfBad;
    }

    public function setPercentageOfBad(?float $percentageOfBad): self
    {
        $this->percentageOfBad = $percentageOfBad;
        return $this;
    }

    public function getTips(): ?string
    {
        return $this->tips;
    }

    public function setTips(?string $tips): self
    {
        $this->tips = $tips;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'percentageOfGood' => $this->percentageOfGood,
            'percentageOfAverage' => $this->percentageOfAverage,
            'percentageOfBad' => $this->percentageOfBad,
            'tips' => $this->tips ?? '',
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $category = new self();
        $category->setPercentageOfGood($data['percentageOfGood'] ?? null);
        $category->setPercentageOfAverage($data['percentageOfAverage'] ?? null);
        $category->setPercentageOfBad($data['percentageOfBad'] ?? null);
        $category->setTips($data['tips'] ?? null);

        return $category;
    }

    /**
     * Get total percentage (should be 100% ideally)
     */
    public function getTotalPercentage(): float
    {
        return ($this->percentageOfGood ?? 0) + 
               ($this->percentageOfAverage ?? 0) + 
               ($this->percentageOfBad ?? 0);
    }

    /**
     * Check if the category has valid percentages
     */
    public function isValid(): bool
    {
        $total = $this->getTotalPercentage();
        return $total >= 99.0 && $total <= 101.0; // Allow small rounding errors
    }
}
