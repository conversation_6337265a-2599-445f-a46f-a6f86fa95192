<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class SubScoreData
{
    #[MongoDB\EmbedOne(targetDocument: ScoreCategory::class)]
    private ?ScoreCategory $dynamics = null;

    #[MongoDB\EmbedOne(targetDocument: ScoreCategory::class)]
    private ?ScoreCategory $deceleration = null;

    #[MongoDB\EmbedOne(targetDocument: ScoreCategory::class)]
    private ?ScoreCategory $cornering = null;

    public function getDynamics(): ?ScoreCategory
    {
        return $this->dynamics;
    }

    public function setDynamics(?ScoreCategory $dynamics): self
    {
        $this->dynamics = $dynamics;
        return $this;
    }

    public function getDeceleration(): ?ScoreCategory
    {
        return $this->deceleration;
    }

    public function setDeceleration(?ScoreCategory $deceleration): self
    {
        $this->deceleration = $deceleration;
        return $this;
    }

    public function getCornering(): ?ScoreCategory
    {
        return $this->cornering;
    }

    public function setCornering(?ScoreCategory $cornering): self
    {
        $this->cornering = $cornering;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     * Note: Uses 'dynamincs' and 'decelaration' to match existing API format
     */
    public function toArray(): array
    {
        return [
            'dynamincs' => $this->dynamics?->toArray() ?? [
                'percentageOfGood' => null,
                'percentageOfAverage' => null,
                'percentageOfBad' => null,
                'tips' => ''
            ],
            'decelaration' => $this->deceleration?->toArray() ?? [
                'percentageOfGood' => null,
                'percentageOfAverage' => null,
                'percentageOfBad' => null,
                'tips' => ''
            ],
            'cornering' => $this->cornering?->toArray() ?? [
                'percentageOfGood' => null,
                'percentageOfAverage' => null,
                'percentageOfBad' => null,
                'tips' => ''
            ],
        ];
    }

    /**
     * Create from array data for backward compatibility
     * Note: Handles both 'dynamincs'/'decelaration' (API format) and 'dynamics'/'deceleration' (correct spelling)
     */
    public static function fromArray(array $data): self
    {
        $subScore = new self();

        // Handle dynamics (with typo support)
        $dynamicsData = $data['dynamincs'] ?? $data['dynamics'] ?? null;
        if ($dynamicsData) {
            $subScore->setDynamics(ScoreCategory::fromArray($dynamicsData));
        }

        // Handle deceleration (with typo support)
        $decelerationData = $data['decelaration'] ?? $data['deceleration'] ?? null;
        if ($decelerationData) {
            $subScore->setDeceleration(ScoreCategory::fromArray($decelerationData));
        }

        // Handle cornering
        if (isset($data['cornering'])) {
            $subScore->setCornering(ScoreCategory::fromArray($data['cornering']));
        }

        return $subScore;
    }

    /**
     * Get overall driving quality assessment
     */
    public function getOverallQuality(): string
    {
        $categories = [$this->dynamics, $this->deceleration, $this->cornering];
        $totalGood = 0;
        $totalBad = 0;
        $count = 0;

        foreach ($categories as $category) {
            if ($category) {
                $totalGood += $category->getPercentageOfGood() ?? 0;
                $totalBad += $category->getPercentageOfBad() ?? 0;
                $count++;
            }
        }

        if ($count === 0) {
            return 'unknown';
        }

        $avgGood = $totalGood / $count;
        $avgBad = $totalBad / $count;

        if ($avgGood >= 70) {
            return 'excellent';
        } elseif ($avgGood >= 50) {
            return 'good';
        } elseif ($avgBad <= 20) {
            return 'average';
        } else {
            return 'needs_improvement';
        }
    }
}
