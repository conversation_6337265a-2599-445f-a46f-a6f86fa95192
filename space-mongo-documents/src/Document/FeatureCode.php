<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class FeatureCode
{
    #[MongoDB\Field(type: 'string')]
    private ?string $code = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $status = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $value = null;

    #[MongoDB\Field(type: 'hash')]
    private array $config = [];

    #[MongoDB\Field(type: 'int')]
    private ?int $calcTimestamp = null;

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): self
    {
        $this->config = $config;
        return $this;
    }

    public function getCalcTimestamp(): ?int
    {
        return $this->calcTimestamp;
    }

    public function setCalcTimestamp(?int $calcTimestamp): self
    {
        $this->calcTimestamp = $calcTimestamp;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        $result = [
            'code' => $this->code,
            'status' => $this->status,
            'value' => $this->value,
            'config' => $this->config,
        ];

        if ($this->calcTimestamp !== null) {
            $result['calcTimestamp'] = $this->calcTimestamp;
        }

        return $result;
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $featureCode = new self();
        $featureCode->setCode($data['code'] ?? null);
        $featureCode->setStatus($data['status'] ?? null);
        $featureCode->setValue($data['value'] ?? null);
        $featureCode->setConfig($data['config'] ?? []);
        $featureCode->setCalcTimestamp($data['calcTimestamp'] ?? null);

        return $featureCode;
    }

    /**
     * Check if feature code is enabled
     */
    public function isEnabled(): bool
    {
        return $this->status === 'enable' || $this->status === 'enabled';
    }

    /**
     * Check if feature code is disabled
     */
    public function isDisabled(): bool
    {
        return $this->status === 'disable' || $this->status === 'disabled';
    }
}
