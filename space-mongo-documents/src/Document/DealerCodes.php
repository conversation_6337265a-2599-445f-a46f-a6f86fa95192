<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class DealerCodes
{
    #[MongoDB\Field(type: 'string')]
    private ?string $codeActeur = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $codeActeurPrincipal = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $codeNature = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $dealerCode = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $idSite = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $mainDealerCode = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $market = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $oic = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $outlet = null;

    public function getCodeActeur(): ?string
    {
        return $this->codeActeur;
    }

    public function setCodeActeur(?string $codeActeur): self
    {
        $this->codeActeur = $codeActeur;
        return $this;
    }

    public function getCodeActeurPrincipal(): ?string
    {
        return $this->codeActeurPrincipal;
    }

    public function setCodeActeurPrincipal(?string $codeActeurPrincipal): self
    {
        $this->codeActeurPrincipal = $codeActeurPrincipal;
        return $this;
    }

    public function getCodeNature(): ?string
    {
        return $this->codeNature;
    }

    public function setCodeNature(?string $codeNature): self
    {
        $this->codeNature = $codeNature;
        return $this;
    }

    public function getDealerCode(): ?string
    {
        return $this->dealerCode;
    }

    public function setDealerCode(?string $dealerCode): self
    {
        $this->dealerCode = $dealerCode;
        return $this;
    }

    public function getIdSite(): ?string
    {
        return $this->idSite;
    }

    public function setIdSite(?string $idSite): self
    {
        $this->idSite = $idSite;
        return $this;
    }

    public function getMainDealerCode(): ?string
    {
        return $this->mainDealerCode;
    }

    public function setMainDealerCode(?string $mainDealerCode): self
    {
        $this->mainDealerCode = $mainDealerCode;
        return $this;
    }

    public function getMarket(): ?string
    {
        return $this->market;
    }

    public function setMarket(?string $market): self
    {
        $this->market = $market;
        return $this;
    }

    public function getOic(): ?string
    {
        return $this->oic;
    }

    public function setOic(?string $oic): self
    {
        $this->oic = $oic;
        return $this;
    }

    public function getOutlet(): ?string
    {
        return $this->outlet;
    }

    public function setOutlet(?string $outlet): self
    {
        $this->outlet = $outlet;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'codeActeur' => $this->codeActeur,
            'codeActeurPrincipal' => $this->codeActeurPrincipal,
            'codeNature' => $this->codeNature,
            'dealerCode' => $this->dealerCode,
            'idSite' => $this->idSite,
            'mainDealerCode' => $this->mainDealerCode,
            'market' => $this->market,
            'oic' => $this->oic,
            'outlet' => $this->outlet,
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $codes = new self();
        $codes->setCodeActeur($data['codeActeur'] ?? null);
        $codes->setCodeActeurPrincipal($data['codeActeurPrincipal'] ?? null);
        $codes->setCodeNature($data['codeNature'] ?? null);
        $codes->setDealerCode($data['dealerCode'] ?? null);
        $codes->setIdSite($data['idSite'] ?? null);
        $codes->setMainDealerCode($data['mainDealerCode'] ?? null);
        $codes->setMarket($data['market'] ?? null);
        $codes->setOic($data['oic'] ?? null);
        $codes->setOutlet($data['outlet'] ?? null);

        return $codes;
    }

    /**
     * Get primary dealer identifier
     */
    public function getPrimaryId(): ?string
    {
        return $this->dealerCode ?: $this->idSite ?: $this->codeActeur;
    }

    /**
     * Check if this is a main dealer
     */
    public function isMainDealer(): bool
    {
        return $this->dealerCode === $this->mainDealerCode;
    }

    /**
     * Check if dealer codes are complete
     */
    public function isComplete(): bool
    {
        return !empty($this->dealerCode) && !empty($this->market);
    }

    /**
     * Get dealer hierarchy level
     */
    public function getHierarchyLevel(): string
    {
        if ($this->isMainDealer()) {
            return 'main';
        }

        if (!empty($this->mainDealerCode)) {
            return 'subsidiary';
        }

        return 'independent';
    }

    /**
     * Get market-specific dealer code
     */
    public function getMarketDealerCode(): string
    {
        if (empty($this->market) || empty($this->dealerCode)) {
            return $this->dealerCode ?? '';
        }

        return $this->market . '-' . $this->dealerCode;
    }

    /**
     * Validate dealer codes
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->dealerCode)) {
            $errors[] = 'Dealer code is required';
        }

        if (empty($this->market)) {
            $errors[] = 'Market is required';
        }

        if (!empty($this->mainDealerCode) && $this->dealerCode === $this->mainDealerCode) {
            // This is valid - dealer is its own main dealer
        } elseif (!empty($this->mainDealerCode) && empty($this->dealerCode)) {
            $errors[] = 'Dealer code is required when main dealer code is specified';
        }

        return $errors;
    }

    /**
     * Check if dealer belongs to a specific market
     */
    public function belongsToMarket(string $market): bool
    {
        return strcasecmp($this->market ?? '', $market) === 0;
    }
}
