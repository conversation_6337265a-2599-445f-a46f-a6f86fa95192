<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class MileageData
{
    #[MongoDB\Field(type: 'int')]
    private ?int $value = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $date = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $timestamp = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $unit = 'km';

    public function getValue(): ?int
    {
        return $this->value;
    }

    public function setValue(?int $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function getDate(): ?\DateTime
    {
        return $this->date;
    }

    public function setDate(?\DateTime $date): self
    {
        $this->date = $date;
        return $this;
    }

    public function getTimestamp(): ?int
    {
        return $this->timestamp;
    }

    public function setTimestamp(?int $timestamp): self
    {
        $this->timestamp = $timestamp;
        
        // Auto-set date from timestamp if provided
        if ($timestamp !== null) {
            $this->date = new \DateTime('@' . $timestamp);
        }
        
        return $this;
    }

    public function getUnit(): ?string
    {
        return $this->unit;
    }

    public function setUnit(?string $unit): self
    {
        $this->unit = $unit;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        $result = [
            'value' => $this->value,
        ];

        if ($this->date) {
            $result['date'] = $this->date->getTimestamp();
        } elseif ($this->timestamp) {
            $result['date'] = $this->timestamp;
        }

        if ($this->unit && $this->unit !== 'km') {
            $result['unit'] = $this->unit;
        }

        return $result;
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $mileage = new self();
        $mileage->setValue($data['value'] ?? null);
        $mileage->setUnit($data['unit'] ?? 'km');

        // Handle date/timestamp
        if (isset($data['date'])) {
            if (is_int($data['date'])) {
                $mileage->setTimestamp($data['date']);
            } elseif ($data['date'] instanceof \DateTime) {
                $mileage->setDate($data['date']);
            }
        }

        return $mileage;
    }

    /**
     * Get mileage in miles (if stored in km)
     */
    public function getValueInMiles(): ?float
    {
        if ($this->value === null) {
            return null;
        }

        if ($this->unit === 'miles') {
            return (float) $this->value;
        }

        // Convert km to miles
        return round($this->value * 0.621371, 2);
    }

    /**
     * Get mileage in kilometers (if stored in miles)
     */
    public function getValueInKm(): ?float
    {
        if ($this->value === null) {
            return null;
        }

        if ($this->unit === 'km') {
            return (float) $this->value;
        }

        // Convert miles to km
        return round($this->value * 1.60934, 2);
    }

    /**
     * Check if mileage data is recent (within last 30 days)
     */
    public function isRecent(): bool
    {
        if (!$this->date) {
            return false;
        }

        $thirtyDaysAgo = new \DateTime('-30 days');
        return $this->date >= $thirtyDaysAgo;
    }

    /**
     * Get formatted date string
     */
    public function getFormattedDate(string $format = 'Y-m-d'): ?string
    {
        return $this->date?->format($format);
    }
}
