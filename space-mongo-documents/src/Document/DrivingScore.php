<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Space\MongoDocuments\Repository\DrivingScoreRepository;

#[MongoDB\Document(collection: 'drivingScore', repositoryClass: DrivingScoreRepository::class)]
class DrivingScore
{
    #[MongoDB\Id]
    #[Serializer\SerializedName("_id")]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $vin = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $contractNumber = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $stliPolicyNumber = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $validFlag = null;

    #[MongoDB\Field(type: 'bool')]
    private ?bool $isValid = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $globalScore = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $accelerationScore = null;

    #[MongoDB\Field(type: 'int')]
    private ?int $brakingScore = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $lastUpdate = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $updateDate = null;

    #[MongoDB\Field(type: 'collection')]
    private array $overallScore = [];

    #[MongoDB\Field(type: 'collection')]
    private array $dailyScore = [];

    #[MongoDB\EmbedOne(targetDocument: ScoreData::class)]
    private ?ScoreData $overallScoreData = null;

    #[MongoDB\EmbedOne(targetDocument: ScoreData::class)]
    private ?ScoreData $dailyScoreData = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getVin(): ?string
    {
        return $this->vin;
    }

    public function setVin(?string $vin): self
    {
        $this->vin = $vin;
        return $this;
    }

    public function getContractNumber(): ?string
    {
        return $this->contractNumber;
    }

    public function setContractNumber(?string $contractNumber): self
    {
        $this->contractNumber = $contractNumber;
        return $this;
    }

    public function getStliPolicyNumber(): ?string
    {
        return $this->stliPolicyNumber;
    }

    public function setStliPolicyNumber(?string $stliPolicyNumber): self
    {
        $this->stliPolicyNumber = $stliPolicyNumber;
        return $this;
    }

    public function getValidFlag(): ?string
    {
        return $this->validFlag;
    }

    public function setValidFlag(?string $validFlag): self
    {
        $this->validFlag = $validFlag;
        return $this;
    }

    public function getIsValid(): ?bool
    {
        return $this->isValid;
    }

    public function setIsValid(?bool $isValid): self
    {
        $this->isValid = $isValid;
        return $this;
    }

    public function getGlobalScore(): ?int
    {
        return $this->globalScore;
    }

    public function setGlobalScore(?int $globalScore): self
    {
        $this->globalScore = $globalScore;
        return $this;
    }

    public function getAccelerationScore(): ?int
    {
        return $this->accelerationScore;
    }

    public function setAccelerationScore(?int $accelerationScore): self
    {
        $this->accelerationScore = $accelerationScore;
        return $this;
    }

    public function getBrakingScore(): ?int
    {
        return $this->brakingScore;
    }

    public function setBrakingScore(?int $brakingScore): self
    {
        $this->brakingScore = $brakingScore;
        return $this;
    }

    public function getLastUpdate(): ?\DateTime
    {
        return $this->lastUpdate;
    }

    public function setLastUpdate(?\DateTime $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;
        return $this;
    }

    public function getUpdateDate(): ?string
    {
        return $this->updateDate;
    }

    public function setUpdateDate(?string $updateDate): self
    {
        $this->updateDate = $updateDate;
        return $this;
    }

    public function getOverallScore(): array
    {
        return $this->overallScore;
    }

    public function setOverallScore(array $overallScore): self
    {
        $this->overallScore = $overallScore;
        return $this;
    }

    public function getDailyScore(): array
    {
        return $this->dailyScore;
    }

    public function setDailyScore(array $dailyScore): self
    {
        $this->dailyScore = $dailyScore;
        return $this;
    }

    // Enhanced ScoreData methods using embedded documents

    /**
     * Get overall score data as ScoreData object
     */
    public function getOverallScoreData(): ?ScoreData
    {
        return $this->overallScoreData;
    }

    /**
     * Set overall score data
     */
    public function setOverallScoreData(?ScoreData $overallScoreData): self
    {
        $this->overallScoreData = $overallScoreData;
        return $this;
    }

    /**
     * Get daily score data as ScoreData object
     */
    public function getDailyScoreData(): ?ScoreData
    {
        return $this->dailyScoreData;
    }

    /**
     * Set daily score data
     */
    public function setDailyScoreData(?ScoreData $dailyScoreData): self
    {
        $this->dailyScoreData = $dailyScoreData;
        return $this;
    }

    /**
     * Set overall score from array and sync to embedded document
     */
    public function setOverallScoreWithSync(array $overallScore): self
    {
        $this->overallScore = $overallScore;
        $this->overallScoreData = ScoreData::fromArray($overallScore);
        return $this;
    }

    /**
     * Set daily score from array and sync to embedded document
     */
    public function setDailyScoreWithSync(array $dailyScore): self
    {
        $this->dailyScore = $dailyScore;
        $this->dailyScoreData = ScoreData::fromArray($dailyScore);
        return $this;
    }

    /**
     * Get overall score as array with sync from embedded document
     */
    public function getOverallScoreWithSync(): array
    {
        if ($this->overallScoreData) {
            $this->overallScore = $this->overallScoreData->toArray();
        }
        return $this->overallScore;
    }

    /**
     * Get daily score as array with sync from embedded document
     */
    public function getDailyScoreWithSync(): array
    {
        if ($this->dailyScoreData) {
            $this->dailyScore = $this->dailyScoreData->toArray();
        }
        return $this->dailyScore;
    }

    /**
     * Sync scores from arrays to embedded documents
     */
    public function syncScoresFromArrays(): self
    {
        if (!empty($this->overallScore)) {
            $this->overallScoreData = ScoreData::fromArray($this->overallScore);
        }

        if (!empty($this->dailyScore)) {
            $this->dailyScoreData = ScoreData::fromArray($this->dailyScore);
        }

        return $this;
    }

    /**
     * Sync scores from embedded documents to arrays
     */
    public function syncScoresToArrays(): self
    {
        if ($this->overallScoreData) {
            $this->overallScore = $this->overallScoreData->toArray();
        }

        if ($this->dailyScoreData) {
            $this->dailyScore = $this->dailyScoreData->toArray();
        }

        return $this;
    }

    /**
     * Auto-sync method to be called before persistence
     */
    public function prePersist(): void
    {
        // Sync embedded documents to arrays for backward compatibility
        $this->syncScoresToArrays();
    }

    /**
     * Auto-sync method to be called after loading from database
     */
    public function postLoad(): void
    {
        // Sync arrays to embedded documents for enhanced functionality
        $this->syncScoresFromArrays();
    }

    /**
     * Get driving quality assessment based on scores
     */
    public function getDrivingQuality(): string
    {
        $overallValue = $this->overallScoreData?->getValue() ?? $this->globalScore;

        if ($overallValue === null) {
            return 'unknown';
        }

        if ($overallValue >= 80) {
            return 'excellent';
        } elseif ($overallValue >= 60) {
            return 'good';
        } elseif ($overallValue >= 40) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Check if driving score has detailed breakdown
     */
    public function hasDetailedBreakdown(): bool
    {
        return $this->dailyScoreData && $this->dailyScoreData->hasSubScore();
    }
}
