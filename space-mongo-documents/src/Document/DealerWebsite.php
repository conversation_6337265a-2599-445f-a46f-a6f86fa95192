<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class DealerWebsite
{
    #[MongoDB\Field(type: 'string')]
    private ?string $url = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $urlApv = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $urlPr = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $urlVn = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $urlVo = null;

    #[MongoDB\Field(type: 'collection')]
    private array $urlPages = [];

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): self
    {
        $this->url = $url;
        return $this;
    }

    public function getUrlApv(): ?string
    {
        return $this->urlApv;
    }

    public function setUrlApv(?string $urlApv): self
    {
        $this->urlApv = $urlApv;
        return $this;
    }

    public function getUrlPr(): ?string
    {
        return $this->urlPr;
    }

    public function setUrlPr(?string $urlPr): self
    {
        $this->urlPr = $urlPr;
        return $this;
    }

    public function getUrlVn(): ?string
    {
        return $this->urlVn;
    }

    public function setUrlVn(?string $urlVn): self
    {
        $this->urlVn = $urlVn;
        return $this;
    }

    public function getUrlVo(): ?string
    {
        return $this->urlVo;
    }

    public function setUrlVo(?string $urlVo): self
    {
        $this->urlVo = $urlVo;
        return $this;
    }

    public function getUrlPages(): array
    {
        return $this->urlPages;
    }

    public function setUrlPages(array $urlPages): self
    {
        $this->urlPages = $urlPages;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'url' => $this->url,
            'urlApv' => $this->urlApv,
            'urlPr' => $this->urlPr,
            'urlVn' => $this->urlVn,
            'urlVo' => $this->urlVo,
            'urlPages' => $this->urlPages,
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $website = new self();
        $website->setUrl($data['url'] ?? null);
        $website->setUrlApv($data['urlApv'] ?? null);
        $website->setUrlPr($data['urlPr'] ?? null);
        $website->setUrlVn($data['urlVn'] ?? null);
        $website->setUrlVo($data['urlVo'] ?? null);
        $website->setUrlPages($data['urlPages'] ?? []);

        return $website;
    }

    /**
     * Add a URL page
     */
    public function addUrlPage(string $type, string $url, string $label = ''): self
    {
        $this->urlPages[] = [
            'type' => $type,
            'url' => $url,
            'label' => $label,
        ];

        return $this;
    }

    /**
     * Get primary website URL
     */
    public function getPrimaryUrl(): ?string
    {
        return $this->url ?: $this->urlVn ?: $this->urlVo;
    }

    /**
     * Get service URL (APV)
     */
    public function getServiceUrl(): ?string
    {
        return $this->urlApv;
    }

    /**
     * Get parts URL (PR)
     */
    public function getPartsUrl(): ?string
    {
        return $this->urlPr;
    }

    /**
     * Get new vehicles URL (VN)
     */
    public function getNewVehiclesUrl(): ?string
    {
        return $this->urlVn;
    }

    /**
     * Get used vehicles URL (VO)
     */
    public function getUsedVehiclesUrl(): ?string
    {
        return $this->urlVo;
    }

    /**
     * Get URL by type
     */
    public function getUrlByType(string $type): ?string
    {
        return match (strtolower($type)) {
            'main', 'primary' => $this->url,
            'apv', 'service' => $this->urlApv,
            'pr', 'parts' => $this->urlPr,
            'vn', 'new' => $this->urlVn,
            'vo', 'used' => $this->urlVo,
            default => null,
        };
    }

    /**
     * Get URL page by type
     */
    public function getUrlPageByType(string $type): ?array
    {
        foreach ($this->urlPages as $page) {
            if (($page['type'] ?? '') === $type) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Check if website information is available
     */
    public function hasWebsiteInfo(): bool
    {
        return $this->getPrimaryUrl() !== null || !empty($this->urlPages);
    }

    /**
     * Get all available URLs
     */
    public function getAllUrls(): array
    {
        return array_filter([
            'main' => $this->url,
            'apv' => $this->urlApv,
            'pr' => $this->urlPr,
            'vn' => $this->urlVn,
            'vo' => $this->urlVo,
        ]);
    }

    /**
     * Validate URL format
     */
    public function validateUrls(): array
    {
        $errors = [];
        $urls = $this->getAllUrls();

        foreach ($urls as $type => $url) {
            if ($url && !filter_var($url, FILTER_VALIDATE_URL)) {
                $errors[] = "Invalid URL format for {$type}: {$url}";
            }
        }

        foreach ($this->urlPages as $index => $page) {
            $url = $page['url'] ?? '';
            if ($url && !filter_var($url, FILTER_VALIDATE_URL)) {
                $errors[] = "Invalid URL format in page {$index}: {$url}";
            }
        }

        return $errors;
    }
}
