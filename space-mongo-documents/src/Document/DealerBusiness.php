<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class DealerBusiness
{
    #[MongoDB\Field(type: 'string')]
    private ?string $businessType = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $businessTypeCode = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $businessTypeLabel = null;

    #[MongoDB\Field(type: 'collection')]
    private array $openingHours = [];

    #[MongoDB\Field(type: 'collection')]
    private array $indicators = [];

    public function getBusinessType(): ?string
    {
        return $this->businessType;
    }

    public function setBusinessType(?string $businessType): self
    {
        $this->businessType = $businessType;
        return $this;
    }

    public function getBusinessTypeCode(): ?string
    {
        return $this->businessTypeCode;
    }

    public function setBusinessTypeCode(?string $businessTypeCode): self
    {
        $this->businessTypeCode = $businessTypeCode;
        return $this;
    }

    public function getBusinessTypeLabel(): ?string
    {
        return $this->businessTypeLabel;
    }

    public function setBusinessTypeLabel(?string $businessTypeLabel): self
    {
        $this->businessTypeLabel = $businessTypeLabel;
        return $this;
    }

    public function getOpeningHours(): array
    {
        return $this->openingHours;
    }

    public function setOpeningHours(array $openingHours): self
    {
        $this->openingHours = $openingHours;
        return $this;
    }

    public function getIndicators(): array
    {
        return $this->indicators;
    }

    public function setIndicators(array $indicators): self
    {
        $this->indicators = $indicators;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'businessType' => $this->businessType,
            'businessTypeCode' => $this->businessTypeCode,
            'businessTypeLabel' => $this->businessTypeLabel,
            'openingHours' => $this->openingHours,
            'indicators' => $this->indicators,
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $business = new self();
        $business->setBusinessType($data['businessType'] ?? null);
        $business->setBusinessTypeCode($data['businessTypeCode'] ?? null);
        $business->setBusinessTypeLabel($data['businessTypeLabel'] ?? null);
        $business->setOpeningHours($data['openingHours'] ?? []);
        $business->setIndicators($data['indicators'] ?? []);

        return $business;
    }

    /**
     * Add opening hours for a specific day
     */
    public function addOpeningHour(string $day, string $openTime, string $closeTime): self
    {
        $this->openingHours[] = [
            'day' => $day,
            'openTime' => $openTime,
            'closeTime' => $closeTime,
        ];

        return $this;
    }

    /**
     * Add an indicator
     */
    public function addIndicator(string $code, string $label, bool $value = true): self
    {
        $this->indicators[] = [
            'code' => $code,
            'label' => $label,
            'value' => $value,
        ];

        return $this;
    }

    /**
     * Check if dealer is open on a specific day
     */
    public function isOpenOnDay(string $day): bool
    {
        foreach ($this->openingHours as $hours) {
            if (strtolower($hours['day'] ?? '') === strtolower($day)) {
                return !empty($hours['openTime']) && !empty($hours['closeTime']);
            }
        }

        return false;
    }

    /**
     * Get opening hours for a specific day
     */
    public function getOpeningHoursForDay(string $day): ?array
    {
        foreach ($this->openingHours as $hours) {
            if (strtolower($hours['day'] ?? '') === strtolower($day)) {
                return $hours;
            }
        }

        return null;
    }

    /**
     * Check if dealer has a specific indicator
     */
    public function hasIndicator(string $code): bool
    {
        foreach ($this->indicators as $indicator) {
            if (($indicator['code'] ?? '') === $code) {
                return (bool) ($indicator['value'] ?? false);
            }
        }

        return false;
    }

    /**
     * Get indicator value
     */
    public function getIndicatorValue(string $code): ?bool
    {
        foreach ($this->indicators as $indicator) {
            if (($indicator['code'] ?? '') === $code) {
                return (bool) ($indicator['value'] ?? false);
            }
        }

        return null;
    }

    /**
     * Check if dealer is currently open (requires current time)
     */
    public function isCurrentlyOpen(\DateTime $currentTime = null): bool
    {
        if ($currentTime === null) {
            $currentTime = new \DateTime();
        }

        $dayName = $currentTime->format('l'); // Full day name
        $currentTimeStr = $currentTime->format('H:i');

        $dayHours = $this->getOpeningHoursForDay($dayName);
        if (!$dayHours) {
            return false;
        }

        $openTime = $dayHours['openTime'] ?? '';
        $closeTime = $dayHours['closeTime'] ?? '';

        if (empty($openTime) || empty($closeTime)) {
            return false;
        }

        return $currentTimeStr >= $openTime && $currentTimeStr <= $closeTime;
    }

    /**
     * Get business type display name
     */
    public function getBusinessTypeDisplay(): string
    {
        return $this->businessTypeLabel ?: $this->businessType ?: 'Unknown';
    }
}
