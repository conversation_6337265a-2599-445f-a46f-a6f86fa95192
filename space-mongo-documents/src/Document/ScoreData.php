<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class ScoreData
{
    #[MongoDB\Field(type: 'float')]
    private ?float $value = null;

    #[MongoDB\EmbedOne(targetDocument: SubScoreData::class)]
    private ?SubScoreData $subScore = null;

    public function getValue(): ?float
    {
        return $this->value;
    }

    public function setValue(?float $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function getSubScore(): ?SubScoreData
    {
        return $this->subScore;
    }

    public function setSubScore(?SubScoreData $subScore): self
    {
        $this->subScore = $subScore;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        $result = [
            'value' => $this->value,
        ];

        if ($this->subScore) {
            $result['subScore'] = $this->subScore->toArray();
        }

        return $result;
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $scoreData = new self();
        $scoreData->setValue($data['value'] ?? null);

        if (isset($data['subScore']) && is_array($data['subScore'])) {
            $scoreData->setSubScore(SubScoreData::fromArray($data['subScore']));
        }

        return $scoreData;
    }

    /**
     * Get score as integer (for legacy compatibility)
     */
    public function getValueAsInt(): ?int
    {
        return $this->value !== null ? (int) round($this->value) : null;
    }

    /**
     * Get score as string (for API responses)
     */
    public function getValueAsString(): ?string
    {
        return $this->value !== null ? number_format($this->value, 2) : null;
    }

    /**
     * Check if score is valid (between 0 and 100)
     */
    public function isValid(): bool
    {
        return $this->value !== null && $this->value >= 0 && $this->value <= 100;
    }

    /**
     * Get score category based on value
     */
    public function getCategory(): string
    {
        if ($this->value === null) {
            return 'unknown';
        }

        if ($this->value >= 80) {
            return 'excellent';
        } elseif ($this->value >= 60) {
            return 'good';
        } elseif ($this->value >= 40) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Check if this score has detailed breakdown
     */
    public function hasSubScore(): bool
    {
        return $this->subScore !== null;
    }
}
