<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class DealerCoordinate
{
    #[MongoDB\Field(type: 'float')]
    private ?float $latitude = null;

    #[MongoDB\Field(type: 'float')]
    private ?float $longitude = null;

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function setLatitude(?float $latitude): self
    {
        $this->latitude = $latitude;
        return $this;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function setLongitude(?float $longitude): self
    {
        $this->longitude = $longitude;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $coordinate = new self();
        $coordinate->setLatitude($data['latitude'] ?? $data['lat'] ?? null);
        $coordinate->setLongitude($data['longitude'] ?? $data['lon'] ?? null);

        return $coordinate;
    }

    /**
     * Calculate distance to another coordinate in kilometers
     */
    public function distanceTo(DealerCoordinate $other): ?float
    {
        if (!$this->isValid() || !$other->isValid()) {
            return null;
        }

        $earthRadius = 6371; // Earth's radius in kilometers

        $lat1Rad = deg2rad($this->latitude);
        $lon1Rad = deg2rad($this->longitude);
        $lat2Rad = deg2rad($other->getLatitude());
        $lon2Rad = deg2rad($other->getLongitude());

        $deltaLat = $lat2Rad - $lat1Rad;
        $deltaLon = $lon2Rad - $lon1Rad;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLon / 2) * sin($deltaLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Calculate distance to a point in kilometers
     */
    public function distanceToPoint(float $latitude, float $longitude): ?float
    {
        $point = new self();
        $point->setLatitude($latitude);
        $point->setLongitude($longitude);

        return $this->distanceTo($point);
    }

    /**
     * Check if coordinates are valid
     */
    public function isValid(): bool
    {
        return $this->latitude !== null && 
               $this->longitude !== null &&
               $this->latitude >= -90 && $this->latitude <= 90 &&
               $this->longitude >= -180 && $this->longitude <= 180;
    }

    /**
     * Get coordinates as string
     */
    public function toString(): string
    {
        if (!$this->isValid()) {
            return 'Invalid coordinates';
        }

        return sprintf('%.6f, %.6f', $this->latitude, $this->longitude);
    }

    /**
     * Check if coordinates are within a bounding box
     */
    public function isWithinBounds(float $minLat, float $maxLat, float $minLon, float $maxLon): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        return $this->latitude >= $minLat && $this->latitude <= $maxLat &&
               $this->longitude >= $minLon && $this->longitude <= $maxLon;
    }
}
