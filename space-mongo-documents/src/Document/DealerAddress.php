<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class DealerAddress
{
    #[MongoDB\Field(type: 'string')]
    private ?string $city = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $country = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $line1 = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $line2 = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $line3 = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $region = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $zipCode = null;

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getLine1(): ?string
    {
        return $this->line1;
    }

    public function setLine1(?string $line1): self
    {
        $this->line1 = $line1;
        return $this;
    }

    public function getLine2(): ?string
    {
        return $this->line2;
    }

    public function setLine2(?string $line2): self
    {
        $this->line2 = $line2;
        return $this;
    }

    public function getLine3(): ?string
    {
        return $this->line3;
    }

    public function setLine3(?string $line3): self
    {
        $this->line3 = $line3;
        return $this;
    }

    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function setRegion(?string $region): self
    {
        $this->region = $region;
        return $this;
    }

    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    public function setZipCode(?string $zipCode): self
    {
        $this->zipCode = $zipCode;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'city' => $this->city,
            'country' => $this->country,
            'street1' => $this->line1,
            'street2' => $this->line2,
            'street3' => $this->line3,
            'region' => $this->region,
            'zipCode' => $this->zipCode,
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $address = new self();
        $address->setCity($data['city'] ?? null);
        $address->setCountry($data['country'] ?? null);
        $address->setLine1($data['street1'] ?? $data['line1'] ?? null);
        $address->setLine2($data['street2'] ?? $data['line2'] ?? null);
        $address->setLine3($data['street3'] ?? $data['line3'] ?? null);
        $address->setRegion($data['region'] ?? null);
        $address->setZipCode($data['zipCode'] ?? null);

        return $address;
    }

    /**
     * Get formatted address string
     */
    public function getFormattedAddress(): string
    {
        $parts = array_filter([
            $this->line1,
            $this->line2,
            $this->line3,
            $this->city,
            $this->region,
            $this->zipCode,
            $this->country
        ]);

        return implode(', ', $parts);
    }

    /**
     * Check if address is complete
     */
    public function isComplete(): bool
    {
        return !empty($this->city) && 
               !empty($this->country) && 
               !empty($this->line1);
    }

    /**
     * Get street address (lines 1-3 combined)
     */
    public function getStreetAddress(): string
    {
        $parts = array_filter([$this->line1, $this->line2, $this->line3]);
        return implode(', ', $parts);
    }
}
