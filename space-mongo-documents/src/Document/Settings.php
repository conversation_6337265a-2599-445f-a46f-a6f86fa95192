<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Space\MongoDocuments\Repository\SettingsRepository;

#[MongoDB\Document(collection: 'settings', repositoryClass: SettingsRepository::class)]
class Settings
{
    #[MongoDB\Id]
    #[Serializer\SerializedName("_id")]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $type = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $country = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $culture = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $source = null;

    #[MongoDB\Field(type: 'hash')]
    private array $data = [];

    #[MongoDB\Field(type: 'hash')]
    private array $settingsData = [];

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function setData(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function getCulture(): ?string
    {
        return $this->culture;
    }

    public function setCulture(?string $culture): self
    {
        $this->culture = $culture;
        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): self
    {
        $this->source = $source;
        return $this;
    }

    public function getSettingsData(): array
    {
        return $this->settingsData;
    }

    public function setSettingsData(array $settingsData): self
    {
        $this->settingsData = $settingsData;
        return $this;
    }
}
