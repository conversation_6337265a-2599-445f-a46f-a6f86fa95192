<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Space\MongoDocuments\Repository\DealerRepository;

#[MongoDB\Document(collection: 'dealers', repositoryClass: DealerRepository::class)]
class Dealer
{
    #[MongoDB\Id]
    private ?string $id = null;

    // Core dealer information (matching MongoDB field order)
    #[MongoDB\Field(type: 'string')]
    private ?string $dealerId = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $name = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $market = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $status = null;

    // Embedded documents
    #[MongoDB\EmbedOne(targetDocument: DealerAddress::class)]
    private ?DealerAddress $address = null;

    #[MongoDB\EmbedOne(targetDocument: DealerCoordinate::class)]
    private ?DealerCoordinate $coordinate = null;

    #[MongoDB\EmbedOne(targetDocument: DealerContact::class)]
    private ?DealerContact $contact = null;

    #[MongoDB\EmbedOne(targetDocument: DealerBusiness::class)]
    private ?DealerBusiness $business = null;

    #[MongoDB\EmbedOne(targetDocument: DealerWebsite::class)]
    private ?DealerWebsite $website = null;

    #[MongoDB\EmbedOne(targetDocument: DealerCodes::class)]
    private ?DealerCodes $codes = null;

    // Backward compatibility arrays
    #[MongoDB\Field(type: 'hash')]
    private array $addressData = [];

    #[MongoDB\Field(type: 'hash')]
    private array $coordinateData = [];

    #[MongoDB\Field(type: 'hash')]
    private array $contactData = [];

    #[MongoDB\Field(type: 'hash')]
    private array $businessData = [];

    #[MongoDB\Field(type: 'hash')]
    private array $websiteData = [];

    #[MongoDB\Field(type: 'hash')]
    private array $codesData = [];

    // Metadata
    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $createdAt = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    // Core getters and setters
    public function getId(): ?string
    {
        return $this->id;
    }

    public function getDealerId(): ?string
    {
        return $this->dealerId;
    }

    public function setDealerId(?string $dealerId): self
    {
        $this->dealerId = $dealerId;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getMarket(): ?string
    {
        return $this->market;
    }

    public function setMarket(?string $market): self
    {
        $this->market = $market;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    // Embedded document getters and setters
    public function getAddress(): ?DealerAddress
    {
        return $this->address;
    }

    public function setAddress(?DealerAddress $address): self
    {
        $this->address = $address;
        return $this;
    }

    public function getCoordinate(): ?DealerCoordinate
    {
        return $this->coordinate;
    }

    public function setCoordinate(?DealerCoordinate $coordinate): self
    {
        $this->coordinate = $coordinate;
        return $this;
    }

    public function getContact(): ?DealerContact
    {
        return $this->contact;
    }

    public function setContact(?DealerContact $contact): self
    {
        $this->contact = $contact;
        return $this;
    }

    public function getBusiness(): ?DealerBusiness
    {
        return $this->business;
    }

    public function setBusiness(?DealerBusiness $business): self
    {
        $this->business = $business;
        return $this;
    }

    public function getWebsite(): ?DealerWebsite
    {
        return $this->website;
    }

    public function setWebsite(?DealerWebsite $website): self
    {
        $this->website = $website;
        return $this;
    }

    public function getCodes(): ?DealerCodes
    {
        return $this->codes;
    }

    public function setCodes(?DealerCodes $codes): self
    {
        $this->codes = $codes;
        return $this;
    }

    // Metadata getters and setters
    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    // Backward compatibility arrays
    public function getAddressData(): array
    {
        return $this->addressData;
    }

    public function setAddressData(array $addressData): self
    {
        $this->addressData = $addressData;
        return $this;
    }

    public function getCoordinateData(): array
    {
        return $this->coordinateData;
    }

    public function setCoordinateData(array $coordinateData): self
    {
        $this->coordinateData = $coordinateData;
        return $this;
    }

    public function getContactData(): array
    {
        return $this->contactData;
    }

    public function setContactData(array $contactData): self
    {
        $this->contactData = $contactData;
        return $this;
    }

    public function getBusinessData(): array
    {
        return $this->businessData;
    }

    public function setBusinessData(array $businessData): self
    {
        $this->businessData = $businessData;
        return $this;
    }

    public function getWebsiteData(): array
    {
        return $this->websiteData;
    }

    public function setWebsiteData(array $websiteData): self
    {
        $this->websiteData = $websiteData;
        return $this;
    }

    public function getCodesData(): array
    {
        return $this->codesData;
    }

    public function setCodesData(array $codesData): self
    {
        $this->codesData = $codesData;
        return $this;
    }

    // Business logic methods

    /**
     * Check if dealer is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active' || $this->status === 'enabled';
    }

    /**
     * Check if dealer is currently open
     */
    public function isCurrentlyOpen(\DateTime $currentTime = null): bool
    {
        return $this->business?->isCurrentlyOpen($currentTime) ?? false;
    }

    /**
     * Calculate distance to a point
     */
    public function distanceToPoint(float $latitude, float $longitude): ?float
    {
        return $this->coordinate?->distanceToPoint($latitude, $longitude);
    }

    /**
     * Get primary contact phone
     */
    public function getPrimaryPhone(): ?string
    {
        return $this->contact?->getPrimaryPhone();
    }

    /**
     * Get primary contact email
     */
    public function getPrimaryEmail(): ?string
    {
        return $this->contact?->getPrimaryEmail();
    }

    /**
     * Get primary website URL
     */
    public function getPrimaryWebsite(): ?string
    {
        return $this->website?->getPrimaryUrl();
    }

    /**
     * Get formatted address
     */
    public function getFormattedAddress(): string
    {
        return $this->address?->getFormattedAddress() ?? '';
    }

    /**
     * Check if dealer has complete information
     */
    public function isComplete(): bool
    {
        return !empty($this->name) &&
               !empty($this->dealerId) &&
               $this->address?->isComplete() &&
               $this->coordinate?->isValid() &&
               $this->contact?->hasContactInfo();
    }

    /**
     * Get dealer display name
     */
    public function getDisplayName(): string
    {
        return $this->name ?? $this->dealerId ?? 'Unknown Dealer';
    }

    // Synchronization methods for backward compatibility

    /**
     * Sync embedded documents to arrays
     */
    public function syncToArrays(): self
    {
        if ($this->address) {
            $this->addressData = $this->address->toArray();
        }

        if ($this->coordinate) {
            $this->coordinateData = $this->coordinate->toArray();
        }

        if ($this->contact) {
            $this->contactData = $this->contact->toArray();
        }

        if ($this->business) {
            $this->businessData = $this->business->toArray();
        }

        if ($this->website) {
            $this->websiteData = $this->website->toArray();
        }

        if ($this->codes) {
            $this->codesData = $this->codes->toArray();
        }

        return $this;
    }

    /**
     * Sync arrays to embedded documents
     */
    public function syncFromArrays(): self
    {
        if (!empty($this->addressData)) {
            $this->address = DealerAddress::fromArray($this->addressData);
        }

        if (!empty($this->coordinateData)) {
            $this->coordinate = DealerCoordinate::fromArray($this->coordinateData);
        }

        if (!empty($this->contactData)) {
            $this->contact = DealerContact::fromArray($this->contactData);
        }

        if (!empty($this->businessData)) {
            $this->business = DealerBusiness::fromArray($this->businessData);
        }

        if (!empty($this->websiteData)) {
            $this->website = DealerWebsite::fromArray($this->websiteData);
        }

        if (!empty($this->codesData)) {
            $this->codes = DealerCodes::fromArray($this->codesData);
        }

        return $this;
    }

    /**
     * Convert to array format for API responses
     */
    public function toArray(): array
    {
        $this->syncToArrays();

        return [
            'id' => $this->id,
            'dealerId' => $this->dealerId,
            'name' => $this->name,
            'brand' => $this->brand,
            'market' => $this->market,
            'status' => $this->status,
            'address' => $this->addressData,
            'coordinate' => $this->coordinateData,
            'contact' => $this->contactData,
            'business' => $this->businessData,
            'website' => $this->websiteData,
            'codes' => $this->codesData,
            'createdAt' => $this->createdAt?->format('c'),
            'updatedAt' => $this->updatedAt?->format('c'),
        ];
    }

    /**
     * Create from array data
     */
    public static function fromArray(array $data): self
    {
        $dealer = new self();
        $dealer->setDealerId($data['dealerId'] ?? null);
        $dealer->setName($data['name'] ?? null);
        $dealer->setBrand($data['brand'] ?? null);
        $dealer->setMarket($data['market'] ?? null);
        $dealer->setStatus($data['status'] ?? null);

        // Set array data
        $dealer->setAddressData($data['address'] ?? []);
        $dealer->setCoordinateData($data['coordinate'] ?? []);
        $dealer->setContactData($data['contact'] ?? []);
        $dealer->setBusinessData($data['business'] ?? []);
        $dealer->setWebsiteData($data['website'] ?? []);
        $dealer->setCodesData($data['codes'] ?? []);

        // Sync to embedded documents
        $dealer->syncFromArrays();

        return $dealer;
    }

    /**
     * Auto-sync method to be called before persistence
     */
    public function prePersist(): void
    {
        $this->updatedAt = new \DateTime();
        $this->syncToArrays();
    }

    /**
     * Auto-sync method to be called after loading from database
     */
    public function postLoad(): void
    {
        $this->syncFromArrays();
    }
}
