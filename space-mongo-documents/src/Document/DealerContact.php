<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class DealerContact
{
    // Phone numbers
    #[MongoDB\Field(type: 'string')]
    private ?string $phoneNumber = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $phoneApv = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $phonePr = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $phoneVn = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $phoneVo = null;

    // Email addresses
    #[MongoDB\Field(type: 'string')]
    private ?string $email = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailApv = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailAgent = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailGer = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailGrc = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailPr = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailSales = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $emailVo = null;

    // Phone getters and setters
    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;
        return $this;
    }

    public function getPhoneApv(): ?string
    {
        return $this->phoneApv;
    }

    public function setPhoneApv(?string $phoneApv): self
    {
        $this->phoneApv = $phoneApv;
        return $this;
    }

    public function getPhonePr(): ?string
    {
        return $this->phonePr;
    }

    public function setPhonePr(?string $phonePr): self
    {
        $this->phonePr = $phonePr;
        return $this;
    }

    public function getPhoneVn(): ?string
    {
        return $this->phoneVn;
    }

    public function setPhoneVn(?string $phoneVn): self
    {
        $this->phoneVn = $phoneVn;
        return $this;
    }

    public function getPhoneVo(): ?string
    {
        return $this->phoneVo;
    }

    public function setPhoneVo(?string $phoneVo): self
    {
        $this->phoneVo = $phoneVo;
        return $this;
    }

    // Email getters and setters
    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function getEmailApv(): ?string
    {
        return $this->emailApv;
    }

    public function setEmailApv(?string $emailApv): self
    {
        $this->emailApv = $emailApv;
        return $this;
    }

    public function getEmailAgent(): ?string
    {
        return $this->emailAgent;
    }

    public function setEmailAgent(?string $emailAgent): self
    {
        $this->emailAgent = $emailAgent;
        return $this;
    }

    public function getEmailGer(): ?string
    {
        return $this->emailGer;
    }

    public function setEmailGer(?string $emailGer): self
    {
        $this->emailGer = $emailGer;
        return $this;
    }

    public function getEmailGrc(): ?string
    {
        return $this->emailGrc;
    }

    public function setEmailGrc(?string $emailGrc): self
    {
        $this->emailGrc = $emailGrc;
        return $this;
    }

    public function getEmailPr(): ?string
    {
        return $this->emailPr;
    }

    public function setEmailPr(?string $emailPr): self
    {
        $this->emailPr = $emailPr;
        return $this;
    }

    public function getEmailSales(): ?string
    {
        return $this->emailSales;
    }

    public function setEmailSales(?string $emailSales): self
    {
        $this->emailSales = $emailSales;
        return $this;
    }

    public function getEmailVo(): ?string
    {
        return $this->emailVo;
    }

    public function setEmailVo(?string $emailVo): self
    {
        $this->emailVo = $emailVo;
        return $this;
    }

    /**
     * Convert to array format for backward compatibility
     */
    public function toArray(): array
    {
        return [
            'phones' => [
                'phoneNumber' => $this->phoneNumber,
                'phoneApv' => $this->phoneApv,
                'phonePr' => $this->phonePr,
                'phoneVn' => $this->phoneVn,
                'phoneVo' => $this->phoneVo,
            ],
            'emails' => [
                'email' => $this->email,
                'emailApv' => $this->emailApv,
                'emailAgent' => $this->emailAgent,
                'emailGer' => $this->emailGer,
                'emailGrc' => $this->emailGrc,
                'emailPr' => $this->emailPr,
                'emailSales' => $this->emailSales,
                'emailVo' => $this->emailVo,
            ],
        ];
    }

    /**
     * Create from array data for backward compatibility
     */
    public static function fromArray(array $data): self
    {
        $contact = new self();
        
        // Handle phones
        $phones = $data['phones'] ?? $data;
        $contact->setPhoneNumber($phones['phoneNumber'] ?? null);
        $contact->setPhoneApv($phones['phoneApv'] ?? null);
        $contact->setPhonePr($phones['phonePr'] ?? null);
        $contact->setPhoneVn($phones['phoneVn'] ?? null);
        $contact->setPhoneVo($phones['phoneVo'] ?? null);

        // Handle emails
        $emails = $data['emails'] ?? $data;
        $contact->setEmail($emails['email'] ?? null);
        $contact->setEmailApv($emails['emailApv'] ?? null);
        $contact->setEmailAgent($emails['emailAgent'] ?? null);
        $contact->setEmailGer($emails['emailGer'] ?? null);
        $contact->setEmailGrc($emails['emailGrc'] ?? null);
        $contact->setEmailPr($emails['emailPr'] ?? null);
        $contact->setEmailSales($emails['emailSales'] ?? null);
        $contact->setEmailVo($emails['emailVo'] ?? null);

        return $contact;
    }

    /**
     * Get primary phone number
     */
    public function getPrimaryPhone(): ?string
    {
        return $this->phoneNumber ?: $this->phoneApv ?: $this->phonePr;
    }

    /**
     * Get primary email address
     */
    public function getPrimaryEmail(): ?string
    {
        return $this->email ?: $this->emailSales ?: $this->emailApv;
    }

    /**
     * Check if contact information is available
     */
    public function hasContactInfo(): bool
    {
        return $this->getPrimaryPhone() !== null || $this->getPrimaryEmail() !== null;
    }

    /**
     * Get all available phone numbers
     */
    public function getAllPhones(): array
    {
        return array_filter([
            'main' => $this->phoneNumber,
            'apv' => $this->phoneApv,
            'pr' => $this->phonePr,
            'vn' => $this->phoneVn,
            'vo' => $this->phoneVo,
        ]);
    }

    /**
     * Get all available email addresses
     */
    public function getAllEmails(): array
    {
        return array_filter([
            'main' => $this->email,
            'apv' => $this->emailApv,
            'agent' => $this->emailAgent,
            'ger' => $this->emailGer,
            'grc' => $this->emailGrc,
            'pr' => $this->emailPr,
            'sales' => $this->emailSales,
            'vo' => $this->emailVo,
        ]);
    }
}
