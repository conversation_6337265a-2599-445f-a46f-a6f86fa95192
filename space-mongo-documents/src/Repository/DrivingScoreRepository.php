<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\DrivingScore;

class DrivingScoreRepository extends DocumentRepository
{
    /**
     * Find driving score by VIN
     */
    public function findByVin(string $vin): array
    {
        return $this->findBy(['vin' => $vin]);
    }

    /**
     * Find driving score by VIN and contract number
     */
    public function findByVinAndContractNumber(string $vin, string $contractNumber): ?DrivingScore
    {
        return $this->findOneBy([
            'vin' => $vin,
            'contractNumber' => $contractNumber
        ]);
    }

    /**
     * Find driving score by VIN and STLI policy number
     */
    public function findByVinAndStliPolicyNumber(string $vin, string $stliPolicyNumber): ?DrivingScore
    {
        return $this->findOneBy([
            'vin' => $vin,
            'stliPolicyNumber' => $stliPolicyNumber
        ]);
    }

    /**
     * Find valid driving scores by VIN
     */
    public function findValidByVin(string $vin): array
    {
        return $this->createQueryBuilder()
            ->field('vin')->equals($vin)
            ->field('validFlag')->equals('Y')
            ->getQuery()
            ->execute()
            ->toArray();
    }

    /**
     * Find latest driving score by VIN
     */
    public function findLatestByVin(string $vin): ?DrivingScore
    {
        return $this->createQueryBuilder()
            ->field('vin')->equals($vin)
            ->sort('lastUpdate', 'desc')
            ->limit(1)
            ->getQuery()
            ->getSingleResult();
    }
}
