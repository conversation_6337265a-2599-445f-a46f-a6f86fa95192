<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\Dealer;

class DealerRepository extends DocumentRepository
{
    /**
     * Find dealer by dealer ID
     */
    public function findByDealerId(string $dealerId): ?Dealer
    {
        return $this->findOneBy(['dealerId' => $dealerId]);
    }

    /**
     * Find dealers by brand
     */
    public function findByBrand(string $brand): array
    {
        return $this->findBy(['brand' => $brand]);
    }

    /**
     * Find dealers by market
     */
    public function findByMarket(string $market): array
    {
        return $this->findBy(['market' => $market]);
    }

    /**
     * Find active dealers
     */
    public function findActive(): array
    {
        return $this->findBy(['status' => ['$in' => ['active', 'enabled']]]);
    }

    /**
     * Find dealers near a location
     */
    public function findNearLocation(float $latitude, float $longitude, float $maxDistanceKm = 50): array
    {
        $qb = $this->createQueryBuilder();
        
        // Use MongoDB's geospatial query for better performance
        $qb->field('coordinateData.latitude')->exists(true)
           ->field('coordinateData.longitude')->exists(true);

        $dealers = $qb->getQuery()->execute()->toArray();

        // Filter by distance (could be optimized with MongoDB geospatial indexes)
        $nearbyDealers = [];
        foreach ($dealers as $dealer) {
            $distance = $dealer->distanceToPoint($latitude, $longitude);
            if ($distance !== null && $distance <= $maxDistanceKm) {
                $nearbyDealers[] = [
                    'dealer' => $dealer,
                    'distance' => $distance
                ];
            }
        }

        // Sort by distance
        usort($nearbyDealers, fn($a, $b) => $a['distance'] <=> $b['distance']);

        return array_column($nearbyDealers, 'dealer');
    }

    /**
     * Find dealers by business type
     */
    public function findByBusinessType(string $businessType): array
    {
        return $this->findBy(['businessData.businessType' => $businessType]);
    }

    /**
     * Find dealers with specific indicator
     */
    public function findWithIndicator(string $indicatorCode): array
    {
        $qb = $this->createQueryBuilder();
        $qb->field('businessData.indicators')->elemMatch(
            $qb->expr()
                ->field('code')->equals($indicatorCode)
                ->field('value')->equals(true)
        );

        return $qb->getQuery()->execute()->toArray();
    }

    /**
     * Search dealers by name or dealer ID
     */
    public function search(string $query): array
    {
        $qb = $this->createQueryBuilder();
        
        $regex = new \MongoDB\BSON\Regex($query, 'i'); // Case-insensitive search
        
        $qb->addOr($qb->expr()->field('name')->equals($regex))
           ->addOr($qb->expr()->field('dealerId')->equals($regex));

        return $qb->getQuery()->execute()->toArray();
    }

    /**
     * Find dealers by city
     */
    public function findByCity(string $city): array
    {
        $regex = new \MongoDB\BSON\Regex($city, 'i');
        return $this->findBy(['addressData.city' => $regex]);
    }

    /**
     * Find dealers by country
     */
    public function findByCountry(string $country): array
    {
        return $this->findBy(['addressData.country' => $country]);
    }

    /**
     * Find dealers by postal code
     */
    public function findByPostalCode(string $postalCode): array
    {
        return $this->findBy(['addressData.zipCode' => $postalCode]);
    }

    /**
     * Get dealer statistics
     */
    public function getStatistics(): array
    {
        $qb = $this->createQueryBuilder();
        
        // Total count
        $total = $this->count([]);
        
        // Active count
        $active = $this->count(['status' => ['$in' => ['active', 'enabled']]]);
        
        // By brand
        $brandStats = [];
        $brands = $this->createQueryBuilder()
            ->distinct('brand')
            ->getQuery()
            ->execute()
            ->toArray();
            
        foreach ($brands as $brand) {
            if ($brand) {
                $brandStats[$brand] = $this->count(['brand' => $brand]);
            }
        }

        // By market
        $marketStats = [];
        $markets = $this->createQueryBuilder()
            ->distinct('market')
            ->getQuery()
            ->execute()
            ->toArray();
            
        foreach ($markets as $market) {
            if ($market) {
                $marketStats[$market] = $this->count(['market' => $market]);
            }
        }

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active,
            'byBrand' => $brandStats,
            'byMarket' => $marketStats,
        ];
    }

    /**
     * Find dealers that need data updates (missing coordinates, contact info, etc.)
     */
    public function findIncomplete(): array
    {
        $qb = $this->createQueryBuilder();
        
        $qb->addOr($qb->expr()->field('name')->exists(false))
           ->addOr($qb->expr()->field('name')->equals(''))
           ->addOr($qb->expr()->field('coordinateData.latitude')->exists(false))
           ->addOr($qb->expr()->field('coordinateData.longitude')->exists(false))
           ->addOr($qb->expr()->field('addressData.city')->exists(false))
           ->addOr($qb->expr()->field('addressData.city')->equals(''));

        return $qb->getQuery()->execute()->toArray();
    }

    /**
     * Bulk update dealer status
     */
    public function updateStatusByMarket(string $market, string $status): int
    {
        $qb = $this->createQueryBuilder();
        $qb->updateMany()
           ->field('market')->equals($market)
           ->field('status')->set($status)
           ->field('updatedAt')->set(new \DateTime());

        return $qb->getQuery()->execute()->getModifiedCount();
    }

    /**
     * Find dealers updated since a specific date
     */
    public function findUpdatedSince(\DateTime $since): array
    {
        return $this->findBy(['updatedAt' => ['$gte' => $since]]);
    }

    /**
     * Find main dealers (dealers that are their own main dealer)
     */
    public function findMainDealers(): array
    {
        $qb = $this->createQueryBuilder();
        $qb->field('codesData.dealerCode')->exists(true)
           ->field('codesData.mainDealerCode')->exists(true)
           ->where('this.codesData.dealerCode == this.codesData.mainDealerCode');

        return $qb->getQuery()->execute()->toArray();
    }

    /**
     * Find subsidiary dealers for a main dealer
     */
    public function findSubsidiaries(string $mainDealerCode): array
    {
        $qb = $this->createQueryBuilder();
        $qb->field('codesData.mainDealerCode')->equals($mainDealerCode)
           ->field('codesData.dealerCode')->notEqual($mainDealerCode);

        return $qb->getQuery()->execute()->toArray();
    }
}
