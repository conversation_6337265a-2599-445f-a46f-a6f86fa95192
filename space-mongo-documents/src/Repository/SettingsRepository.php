<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\Settings;

class SettingsRepository extends DocumentRepository
{
    /**
     * Find settings by type
     */
    public function findByType(string $type): array
    {
        return $this->findBy(['type' => $type]);
    }

    /**
     * Find settings by type, brand, and country
     */
    public function findByTypeBrandAndCountry(string $type, string $brand, string $country): ?Settings
    {
        return $this->findOneBy([
            'type' => $type,
            'brand' => $brand,
            'country' => $country
        ]);
    }

    /**
     * Find settings by type and brand
     */
    public function findByTypeAndBrand(string $type, string $brand): array
    {
        return $this->findBy([
            'type' => $type,
            'brand' => $brand
        ]);
    }

    /**
     * Find settings by type and country
     */
    public function findByTypeAndCountry(string $type, string $country): array
    {
        return $this->findBy([
            'type' => $type,
            'country' => $country
        ]);
    }

    /**
     * Find settings by brand and source
     */
    public function findByBrandAndSource(string $brand, string $source): array
    {
        return $this->findBy([
            'brand' => $brand,
            'source' => $source
        ]);
    }

    /**
     * Find settings by complex filter with MongoDB operators
     */
    public function findByComplexFilter(array $filter): ?Settings
    {
        $queryBuilder = $this->createQueryBuilder();

        // Handle basic field filters
        if (isset($filter['brand'])) {
            $queryBuilder->field('brand')->equals($filter['brand']);
        }
        if (isset($filter['source'])) {
            $queryBuilder->field('source')->equals($filter['source']);
        }
        if (isset($filter['culture'])) {
            $queryBuilder->field('culture')->equals($filter['culture']);
        }

        // Handle $or operator for nested settingsData queries
        if (isset($filter['$or']) && is_array($filter['$or'])) {
            $orExpr = $queryBuilder->expr();
            $orConditions = [];

            foreach ($filter['$or'] as $orCondition) {
                if (isset($orCondition['settingsData.o2x.code'])) {
                    $orConditions[] = $orExpr->field('settingsData.o2x.code')->equals($orCondition['settingsData.o2x.code']);
                }
                if (isset($orCondition['settingsData.config.code'])) {
                    $orConditions[] = $orExpr->field('settingsData.config.code')->equals($orCondition['settingsData.config.code']);
                }
            }

            if (!empty($orConditions)) {
                $queryBuilder->addOr(...$orConditions);
            }
        }

        return $queryBuilder->getQuery()->getSingleResult();
    }
}
