<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\UserData;

class UserDataRepository extends DocumentRepository
{
    /**
     * Find user data by user ID
     */
    public function findByUserId(string $userId): ?UserData
    {
        return $this->findOneBy(['userId' => $userId]);
    }

    /**
     * Find user data by VIN
     */
    public function findByVin(string $vin): array
    {
        return $this->createQueryBuilder()
            ->field('vehicle.vin')->equals($vin)
            ->getQuery()
            ->execute()
            ->toArray();
    }

    /**
     * Find user data by user ID and VIN
     */
    public function findByUserIdAndVin(string $userId, string $vin): ?UserData
    {
        return $this->createQueryBuilder()
            ->field('userId')->equals($userId)
            ->field('vehicle.vin')->equals($vin)
            ->getQuery()
            ->getSingleResult();
    }

    /**
     * Find user data by session ID
     */
    public function findBySessionId(string $sessionId): array
    {
        return $this->createQueryBuilder()
            ->field('sessionId')->equals($sessionId)
            ->getQuery()
            ->execute()
            ->toArray();
    }

    /**
     * Find user data by user ID, VIN, and session ID
     */
    public function findByUserIdVinAndSessionId(?string $userId, ?string $vin, ?string $sessionId): array
    {
        $qb = $this->createQueryBuilder();
        
        if ($userId) {
            $qb->field('userId')->equals($userId);
        }
        
        if ($vin) {
            $qb->field('vehicle.vin')->equals($vin);
        }
        
        if ($sessionId) {
            $qb->field('sessionId')->equals($sessionId);
        }
        
        return $qb->getQuery()->execute()->toArray();
    }
}
