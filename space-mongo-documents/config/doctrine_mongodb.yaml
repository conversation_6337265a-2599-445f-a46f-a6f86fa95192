doctrine_mongodb:
  connections:
    default:
      server: "%env(MONGO_DB_URL)%"
      options: {}
  default_database: "%env(MONGODB_DB)%"
  document_managers:
    default:
      auto_mapping: true
      mappings:
        SpaceMongoDocuments:
          is_bundle: false
          type: attribute
          dir: "%kernel.project_dir%/vendor/space/mongo-documents/src/Document"
          prefix: Space\MongoDocuments\Document
          alias: SpaceMongoDocuments
