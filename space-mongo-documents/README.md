# Space MongoDB Documents

This is a shared library for MongoDB document definitions used across Space microservices.

## Installation

There are two ways to include this library in your project:

### Option 1: Direct inclusion in your project

Clone or copy this repository into your project structure:

```bash
git clone <repository-url> space-mongo-documents
# or if you already have the repository locally
cp -r /path/to/space-mongo-documents ./space-mongo-documents
```

Then, add it to your composer.json:

```json
{
    "require": {
        "space/mongo-documents": "*"
    },
    "repositories": [
        {
            "type": "path",
            "url": "./space-mongo-documents"
        }
    ]
}
```

### Option 2: Using Composer directly

If you have access to a private Composer repository where this package is hosted, you can install it directly:

```bash
composer require space/mongo-documents
```

Or add it manually to your composer.json and then update:

```json
{
    "require": {
        "space/mongo-documents": "^1.0"
    }
}
```

```bash
composer update
```

## Verification

After installation, verify that the library is correctly installed by checking that the document classes are available in your vendor directory:

```bash
ls -la vendor/space/mongo-documents/src/Document
```

You should see the document classes like `UserData.php`, `Vehicle.php`, etc.

## Configuration

### Symfony Projects

If you're using Symfony with Doctrine MongoDB ODM, add the following to your `config/packages/doctrine_mongodb.yaml` file:

```yaml
doctrine_mongodb:
  connections:
    default:
      server: "%env(MONGO_DB_URL)%"
      options: {}
  default_database: "%env(MONGODB_DB)%"
  document_managers:
    default:
      auto_mapping: true
      mappings:
        SpaceMongoDocuments:
          is_bundle: false
          type: attribute
          dir: "%kernel.project_dir%/vendor/space/mongo-documents/src/Document"
          prefix: Space\MongoDocuments\Document
          alias: SpaceMongoDocuments
```

### Environment Configuration

Add the following environment variables to your `.env` file:

```
MONGO_DB_URL=mongodb://localhost:27017
MONGODB_DB=space
```

For production environments, you may want to use more specific configuration:

```
MONGO_DB_URL=************************************:port/database?authSource=admin
MONGODB_DB=space_production
```

### Non-Symfony Projects

If you're not using Symfony, you'll need to configure Doctrine MongoDB ODM manually. Here's an example of how to do it:

```php
<?php

use Doctrine\ODM\MongoDB\Configuration;
use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Mapping\Driver\AttributeDriver;
use MongoDB\Client;

// Create a configuration
$config = new Configuration();
$config->setProxyDir(__DIR__ . '/Proxies');
$config->setProxyNamespace('Proxies');
$config->setHydratorDir(__DIR__ . '/Hydrators');
$config->setHydratorNamespace('Hydrators');
$config->setMetadataDriverImpl(new AttributeDriver([__DIR__ . '/vendor/space/mongo-documents/src/Document']));
$config->setDefaultDB('space');

// Create the DocumentManager
$client = new Client('mongodb://localhost:27017');
$dm = DocumentManager::create($client, $config);
```

## Usage

### Using Document Classes

```php
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;

// Create a new user data document
$userData = new UserData();
$userData->setUserId('user123');

// Create a vehicle
$vehicle = new Vehicle();
$vehicle->setVin('VIN123456789');
$vehicle->setBrand('DS');

// Add the vehicle to the user data
$userData->addVehicle($vehicle);

// Save the user data
$documentManager->persist($userData);
$documentManager->flush();
```

### Using the MongoDB Service

The library provides a `MongoDBService` class that simplifies interactions with MongoDB documents. Here's how to use it:

```php
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Service\MongoDBService;
use Psr\Log\LoggerInterface;

// Inject the service in your controller or service
public function __construct(
    private MongoDBService $mongoDBService,
    private LoggerInterface $logger
) {}

// Find a document by ID
$user = $this->mongoDBService->find(UserData::class, 'user123');

// Find documents by criteria with optional sorting, limit, and offset
$users = $this->mongoDBService->findBy(
    UserData::class, 
    ['vehicle.brand' => 'DS'], 
    ['userId' => 'ASC'], 
    10,  // limit
    0    // offset
);

// Find a single document by criteria
$user = $this->mongoDBService->findOneBy(UserData::class, ['userId' => 'user123']);

// Save a new document
$userData = new UserData();
$userData->setUserId('user123');
$this->mongoDBService->save($userData);

// Update an existing document
$user->setUserId('updatedUser123');
$this->mongoDBService->update($user);

// Remove a document
$this->mongoDBService->remove($user);

// Special methods for Settings document
$settings = $this->mongoDBService->findSettingsByTypeBrandAndCountry('config', 'DS', 'FR');

// Access the underlying DocumentManager for advanced operations
$dm = $this->mongoDBService->getDocumentManager();
$qb = $dm->createQueryBuilder(UserData::class);
$qb->field('userId')->equals('user123');
$result = $qb->getQuery()->execute();
```

#### Error Handling

The `MongoDBService` includes comprehensive error logging. All database operations are wrapped in try-catch blocks that log errors through the injected PSR logger. This makes it easy to debug issues in production environments.

## Document Structure

This library follows a structured approach to MongoDB document management:

1. **Document Classes**: PHP classes with MongoDB mapping attributes
2. **Repository Classes**: Custom query methods for each document type
3. **Service Layer**: The `MongoDBService` for standardized database operations

### Document Mapping

All document classes use PHP 8 attributes for MongoDB mapping:

```php
#[MongoDB\Document(collection: 'userData', repositoryClass: UserDataRepository::class)]
class UserData
{
    #[MongoDB\Id]
    private ?string $id = null;
    
    #[MongoDB\Field(type: 'string')]
    private ?string $userId = null;
    
    #[MongoDB\EmbedMany(targetDocument: Vehicle::class)]
    private Collection $vehicle;
}
```

### Embedded Documents

Many documents use embedded documents for structured data:

```php
#[MongoDB\EmbeddedDocument]
class Vehicle
{
    #[MongoDB\Field(type: 'string')]
    private ?string $vin = null;
    
    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;
}
```

## Available Document Classes

### Core User Documents
- [`UserData`](src/Document/UserData.php): Represents user data with vehicles and PSA IDs
  - Stores user identification, vehicles, and preferences
  - Manages preferred dealers by brand
  - Repository: [`UserDataRepository`](src/Repository/UserDataRepository.php)
- [`UserPsaId`](src/Document/UserPsaId.php): Represents a user's PSA ID
  - Embedded in UserData documents

### Vehicle Documents
- [`Vehicle`](src/Document/Vehicle.php): Represents a vehicle with all its details
  - Stores VIN, brand, model, version, registration info
  - Contains feature codes and mileage data
- [`FeatureCode`](src/Document/FeatureCode.php): Represents vehicle feature codes
  - Embedded in Vehicle documents
- [`MileageData`](src/Document/MileageData.php): Represents vehicle mileage information
  - Embedded in Vehicle documents

### Driving Score Documents
- [`DrivingScore`](src/Document/DrivingScore.php): Represents driving score data
  - Stores global, acceleration, and braking scores
  - Contains overall and daily score data
  - Repository: [`DrivingScoreRepository`](src/Repository/DrivingScoreRepository.php)
- [`ScoreData`](src/Document/ScoreData.php): Represents score data structure
  - Embedded in DrivingScore documents
- [`ScoreCategory`](src/Document/ScoreCategory.php): Represents a score category
  - Embedded in ScoreData documents
- [`SubScoreData`](src/Document/SubScoreData.php): Represents sub-score data
  - Embedded in ScoreCategory documents

### Dealer Documents
- [`Dealer`](src/Document/Dealer.php): Represents a dealer with all its details
  - Contains dealer identification, location, and business info
  - Repository: [`DealerRepository`](src/Repository/DealerRepository.php)
- [`DealerAddress`](src/Document/DealerAddress.php): Represents dealer address information
  - Embedded in Dealer documents
- [`DealerBusiness`](src/Document/DealerBusiness.php): Represents dealer business information
  - Embedded in Dealer documents
- [`DealerCodes`](src/Document/DealerCodes.php): Represents dealer identification codes
  - Embedded in Dealer documents
- [`DealerContact`](src/Document/DealerContact.php): Represents dealer contact information
  - Embedded in Dealer documents
- [`DealerCoordinate`](src/Document/DealerCoordinate.php): Represents dealer geographical coordinates
  - Embedded in Dealer documents
- [`DealerWebsite`](src/Document/DealerWebsite.php): Represents dealer website information
  - Embedded in Dealer documents

### Configuration Documents
- [`Settings`](src/Document/Settings.php): Represents application settings
  - Stores configuration settings by type, brand, and country
  - Repository: [`SettingsRepository`](src/Repository/SettingsRepository.php)

## Contributing

### Adding New Document Classes

To add a new document class to this library:

1. Create a new PHP class in the `src/Document` directory
2. Use PHP 8 attributes for document mapping
3. Follow the existing naming conventions
4. Add appropriate getters and setters
5. Update this README to include your new document class

Example:

```php
<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;

#[MongoDB\Document]
class NewDocumentType
{
    #[MongoDB\Id]
    private string $id;
    
    #[MongoDB\Field(type: "string")]
    private string $name;
    
    // Getters and setters
}
```

### Testing

After making changes, run the tests to ensure everything works as expected:

```bash
composer test
```

## Troubleshooting

### Common Issues

1. **Document Not Found**: Ensure your document manager is correctly configured to look for documents in the vendor directory.

2. **MongoDB Connection Issues**: Verify your MongoDB connection string and credentials.

3. **Mapping Errors**: Make sure you're using the correct attribute syntax for your PHP version.

### Getting Help

If you encounter any issues with this library, please contact the Space middleware team.
