<?php

namespace Space\MongoDocuments\Tests\Document;

use PHPUnit\Framework\TestCase;
use Space\MongoDocuments\Document\Dealer;
use Space\MongoDocuments\Document\DealerAddress;
use Space\MongoDocuments\Document\DealerCoordinate;
use Space\MongoDocuments\Document\DealerContact;
use Space\MongoDocuments\Document\DealerBusiness;
use Space\MongoDocuments\Document\DealerWebsite;
use Space\MongoDocuments\Document\DealerCodes;

class DealerDocumentTest extends TestCase
{
    public function testDealerAddressDocument(): void
    {
        $address = new DealerAddress();
        $address->setCity('Paris')
                ->setCountry('France')
                ->setLine1('123 Rue de la Paix')
                ->setLine2('2ème étage')
                ->setRegion('Île-de-France')
                ->setZipCode('75001');

        $this->assertEquals('Paris', $address->getCity());
        $this->assertEquals('France', $address->getCountry());
        $this->assertEquals('123 Rue de la Paix', $address->getLine1());
        $this->assertTrue($address->isComplete());

        // Test array conversion
        $array = $address->toArray();
        $this->assertArrayHasKey('city', $array);
        $this->assertArrayHasKey('street1', $array); // Note: converts line1 to street1
        $this->assertEquals('Paris', $array['city']);

        // Test from array creation
        $newAddress = DealerAddress::fromArray($array);
        $this->assertEquals($address->getCity(), $newAddress->getCity());
        $this->assertEquals($address->getLine1(), $newAddress->getLine1());

        // Test formatted address
        $formatted = $address->getFormattedAddress();
        $this->assertStringContains('Paris', $formatted);
        $this->assertStringContains('France', $formatted);
    }

    public function testDealerCoordinateDocument(): void
    {
        $coordinate = new DealerCoordinate();
        $coordinate->setLatitude(48.8566)
                   ->setLongitude(2.3522);

        $this->assertEquals(48.8566, $coordinate->getLatitude());
        $this->assertEquals(2.3522, $coordinate->getLongitude());
        $this->assertTrue($coordinate->isValid());

        // Test distance calculation
        $otherCoordinate = new DealerCoordinate();
        $otherCoordinate->setLatitude(48.8606)->setLongitude(2.3376); // Louvre coordinates

        $distance = $coordinate->distanceTo($otherCoordinate);
        $this->assertNotNull($distance);
        $this->assertGreaterThan(0, $distance);
        $this->assertLessThan(5, $distance); // Should be less than 5km

        // Test array conversion
        $array = $coordinate->toArray();
        $newCoordinate = DealerCoordinate::fromArray($array);
        $this->assertEquals($coordinate->getLatitude(), $newCoordinate->getLatitude());

        // Test string representation
        $string = $coordinate->toString();
        $this->assertStringContains('48.8566', $string);
    }

    public function testDealerContactDocument(): void
    {
        $contact = new DealerContact();
        $contact->setPhoneNumber('+33 1 23 45 67 89')
                ->setEmail('<EMAIL>')
                ->setEmailSales('<EMAIL>')
                ->setPhoneApv('+33 1 23 45 67 90');

        $this->assertEquals('+33 1 23 45 67 89', $contact->getPhoneNumber());
        $this->assertEquals('<EMAIL>', $contact->getEmail());
        $this->assertTrue($contact->hasContactInfo());

        // Test primary contact methods
        $this->assertEquals('+33 1 23 45 67 89', $contact->getPrimaryPhone());
        $this->assertEquals('<EMAIL>', $contact->getPrimaryEmail());

        // Test get all methods
        $allPhones = $contact->getAllPhones();
        $this->assertArrayHasKey('main', $allPhones);
        $this->assertArrayHasKey('apv', $allPhones);

        $allEmails = $contact->getAllEmails();
        $this->assertArrayHasKey('main', $allEmails);
        $this->assertArrayHasKey('sales', $allEmails);

        // Test array conversion
        $array = $contact->toArray();
        $this->assertArrayHasKey('phones', $array);
        $this->assertArrayHasKey('emails', $array);

        $newContact = DealerContact::fromArray($array);
        $this->assertEquals($contact->getPrimaryPhone(), $newContact->getPrimaryPhone());
    }

    public function testDealerBusinessDocument(): void
    {
        $business = new DealerBusiness();
        $business->setBusinessType('DEALER')
                 ->setBusinessTypeCode('DLR')
                 ->setBusinessTypeLabel('Authorized Dealer');

        // Add opening hours
        $business->addOpeningHour('Monday', '09:00', '18:00');
        $business->addOpeningHour('Tuesday', '09:00', '18:00');

        // Add indicators
        $business->addIndicator('SERVICE', 'Service Available', true);
        $business->addIndicator('PARTS', 'Parts Available', true);

        $this->assertEquals('DEALER', $business->getBusinessType());
        $this->assertTrue($business->isOpenOnDay('Monday'));
        $this->assertFalse($business->isOpenOnDay('Sunday'));
        $this->assertTrue($business->hasIndicator('SERVICE'));
        $this->assertFalse($business->hasIndicator('RENTAL'));

        // Test opening hours for specific day
        $mondayHours = $business->getOpeningHoursForDay('Monday');
        $this->assertNotNull($mondayHours);
        $this->assertEquals('09:00', $mondayHours['openTime']);

        // Test array conversion
        $array = $business->toArray();
        $newBusiness = DealerBusiness::fromArray($array);
        $this->assertEquals($business->getBusinessType(), $newBusiness->getBusinessType());
    }

    public function testDealerWebsiteDocument(): void
    {
        $website = new DealerWebsite();
        $website->setUrl('https://dealer.example.com')
                ->setUrlApv('https://service.dealer.example.com')
                ->setUrlVn('https://new.dealer.example.com');

        // Add URL pages
        $website->addUrlPage('booking', 'https://booking.dealer.example.com', 'Book Service');

        $this->assertEquals('https://dealer.example.com', $website->getUrl());
        $this->assertEquals('https://dealer.example.com', $website->getPrimaryUrl());
        $this->assertEquals('https://service.dealer.example.com', $website->getServiceUrl());
        $this->assertTrue($website->hasWebsiteInfo());

        // Test URL by type
        $this->assertEquals('https://dealer.example.com', $website->getUrlByType('main'));
        $this->assertEquals('https://service.dealer.example.com', $website->getUrlByType('apv'));

        // Test URL page by type
        $bookingPage = $website->getUrlPageByType('booking');
        $this->assertNotNull($bookingPage);
        $this->assertEquals('https://booking.dealer.example.com', $bookingPage['url']);

        // Test validation
        $errors = $website->validateUrls();
        $this->assertEmpty($errors); // All URLs should be valid

        // Test array conversion
        $array = $website->toArray();
        $newWebsite = DealerWebsite::fromArray($array);
        $this->assertEquals($website->getPrimaryUrl(), $newWebsite->getPrimaryUrl());
    }

    public function testDealerCodesDocument(): void
    {
        $codes = new DealerCodes();
        $codes->setDealerCode('FR001')
              ->setMainDealerCode('FR001')
              ->setMarket('FR')
              ->setCodeActeur('ACT001')
              ->setIdSite('SITE001');

        $this->assertEquals('FR001', $codes->getDealerCode());
        $this->assertEquals('FR', $codes->getMarket());
        $this->assertTrue($codes->isMainDealer());
        $this->assertTrue($codes->isComplete());
        $this->assertEquals('main', $codes->getHierarchyLevel());

        // Test market dealer code
        $marketCode = $codes->getMarketDealerCode();
        $this->assertEquals('FR-FR001', $marketCode);

        // Test validation
        $errors = $codes->validate();
        $this->assertEmpty($errors);

        // Test subsidiary dealer
        $subsidiaryCodes = new DealerCodes();
        $subsidiaryCodes->setDealerCode('FR002')
                       ->setMainDealerCode('FR001')
                       ->setMarket('FR');

        $this->assertFalse($subsidiaryCodes->isMainDealer());
        $this->assertEquals('subsidiary', $subsidiaryCodes->getHierarchyLevel());

        // Test array conversion
        $array = $codes->toArray();
        $newCodes = DealerCodes::fromArray($array);
        $this->assertEquals($codes->getDealerCode(), $newCodes->getDealerCode());
    }

    public function testMainDealerDocument(): void
    {
        $dealer = new Dealer();
        $dealer->setDealerId('FR001')
               ->setName('Dealer Paris Centre')
               ->setBrand('Peugeot')
               ->setMarket('FR')
               ->setStatus('active');

        // Set address
        $address = new DealerAddress();
        $address->setCity('Paris')->setCountry('France')->setLine1('123 Rue de la Paix');
        $dealer->setAddress($address);

        // Set coordinates
        $coordinate = new DealerCoordinate();
        $coordinate->setLatitude(48.8566)->setLongitude(2.3522);
        $dealer->setCoordinate($coordinate);

        // Set contact
        $contact = new DealerContact();
        $contact->setPhoneNumber('+33 1 23 45 67 89')->setEmail('<EMAIL>');
        $dealer->setContact($contact);

        // Test business logic methods
        $this->assertTrue($dealer->isActive());
        $this->assertTrue($dealer->isComplete());
        $this->assertEquals('Dealer Paris Centre', $dealer->getDisplayName());
        $this->assertEquals('+33 1 23 45 67 89', $dealer->getPrimaryPhone());
        $this->assertEquals('<EMAIL>', $dealer->getPrimaryEmail());

        // Test distance calculation
        $distance = $dealer->distanceToPoint(48.8606, 2.3376); // Louvre coordinates
        $this->assertNotNull($distance);
        $this->assertLessThan(5, $distance);

        // Test synchronization
        $dealer->syncToArrays();
        $addressData = $dealer->getAddressData();
        $this->assertArrayHasKey('city', $addressData);
        $this->assertEquals('Paris', $addressData['city']);

        // Test array conversion
        $array = $dealer->toArray();
        $this->assertArrayHasKey('dealerId', $array);
        $this->assertArrayHasKey('address', $array);
        $this->assertEquals('FR001', $array['dealerId']);

        // Test from array creation
        $newDealer = Dealer::fromArray($array);
        $this->assertEquals($dealer->getDealerId(), $newDealer->getDealerId());
        $this->assertEquals($dealer->getName(), $newDealer->getName());
        $this->assertEquals($dealer->getAddress()->getCity(), $newDealer->getAddress()->getCity());
    }
}
