<?php

namespace Space\MongoDocuments\Tests\Document;

use PHPUnit\Framework\TestCase;
use Space\MongoDocuments\Document\Settings;

class SettingsTest extends TestCase
{
    private Settings $settings;

    protected function setUp(): void
    {
        $this->settings = new Settings();
    }

    public function testSetAndGetType(): void
    {
        $type = 'configuration';
        $this->settings->setType($type);
        $this->assertEquals($type, $this->settings->getType());
    }

    public function testSetAndGetBrand(): void
    {
        $brand = 'DS';
        $this->settings->setBrand($brand);
        $this->assertEquals($brand, $this->settings->getBrand());
    }

    public function testSetAndGetCountry(): void
    {
        $country = 'FR';
        $this->settings->setCountry($country);
        $this->assertEquals($country, $this->settings->getCountry());
    }

    public function testSetAndGetCulture(): void
    {
        $culture = 'fr-FR';
        $this->settings->setCulture($culture);
        $this->assertEquals($culture, $this->settings->getCulture());
    }

    public function testSetAndGetSource(): void
    {
        $source = 'APP';
        $this->settings->setSource($source);
        $this->assertEquals($source, $this->settings->getSource());
    }

    public function testSetAndGetData(): void
    {
        $data = [
            'feature1' => ['enabled' => true],
            'feature2' => ['enabled' => false, 'config' => ['timeout' => 30]]
        ];
        $this->settings->setData($data);
        $this->assertEquals($data, $this->settings->getData());
    }

    public function testSetAndGetSettingsData(): void
    {
        $settingsData = [
            'o2x' => [
                'enabled' => true,
                'code' => 'o2x',
                'config' => [
                    'showMap' => true,
                    'timeout' => 5000
                ]
            ],
            'config' => [
                'code' => 'o2x',
                'version' => '1.0'
            ]
        ];
        $this->settings->setSettingsData($settingsData);
        $this->assertEquals($settingsData, $this->settings->getSettingsData());
    }

    public function testDefaultValues(): void
    {
        $this->assertNull($this->settings->getId());
        $this->assertNull($this->settings->getType());
        $this->assertNull($this->settings->getBrand());
        $this->assertNull($this->settings->getCountry());
        $this->assertNull($this->settings->getCulture());
        $this->assertNull($this->settings->getSource());
        $this->assertEquals([], $this->settings->getData());
        $this->assertEquals([], $this->settings->getSettingsData());
    }

    public function testFluentInterface(): void
    {
        $result = $this->settings
            ->setType('config')
            ->setBrand('DS')
            ->setCountry('FR')
            ->setCulture('fr-FR')
            ->setSource('APP')
            ->setData(['test' => 'value'])
            ->setSettingsData(['o2x' => ['enabled' => true]]);

        $this->assertInstanceOf(Settings::class, $result);
        $this->assertEquals('config', $this->settings->getType());
        $this->assertEquals('DS', $this->settings->getBrand());
        $this->assertEquals('FR', $this->settings->getCountry());
        $this->assertEquals('fr-FR', $this->settings->getCulture());
        $this->assertEquals('APP', $this->settings->getSource());
        $this->assertEquals(['test' => 'value'], $this->settings->getData());
        $this->assertEquals(['o2x' => ['enabled' => true]], $this->settings->getSettingsData());
    }
}
