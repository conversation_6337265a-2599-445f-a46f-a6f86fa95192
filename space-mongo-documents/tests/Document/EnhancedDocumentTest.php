<?php

namespace Space\MongoDocuments\Tests\Document;

use PHPUnit\Framework\TestCase;
use Space\MongoDocuments\Document\FeatureCode;
use Space\MongoDocuments\Document\ScoreCategory;
use Space\MongoDocuments\Document\SubScoreData;
use Space\MongoDocuments\Document\ScoreData;
use Space\MongoDocuments\Document\MileageData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Document\DrivingScore;
use Doctrine\Common\Collections\ArrayCollection;

class EnhancedDocumentTest extends TestCase
{
    public function testFeatureCodeDocument(): void
    {
        $featureCode = new FeatureCode();
        $featureCode->setCode('UBI_PHYD');
        $featureCode->setStatus('enable');
        $featureCode->setValue('NAE01');
        $featureCode->setConfig(['engine' => 'BEV']);
        $featureCode->setCalcTimestamp(1640995200);

        $this->assertEquals('UBI_PHYD', $featureCode->getCode());
        $this->assertEquals('enable', $featureCode->getStatus());
        $this->assertEquals('NAE01', $featureCode->getValue());
        $this->assertEquals(['engine' => 'BEV'], $featureCode->getConfig());
        $this->assertEquals(1640995200, $featureCode->getCalcTimestamp());
        $this->assertTrue($featureCode->isEnabled());
        $this->assertFalse($featureCode->isDisabled());

        // Test array conversion
        $array = $featureCode->toArray();
        $this->assertArrayHasKey('code', $array);
        $this->assertArrayHasKey('status', $array);
        $this->assertArrayHasKey('value', $array);
        $this->assertArrayHasKey('config', $array);
        $this->assertArrayHasKey('calcTimestamp', $array);

        // Test from array creation
        $newFeatureCode = FeatureCode::fromArray($array);
        $this->assertEquals($featureCode->getCode(), $newFeatureCode->getCode());
        $this->assertEquals($featureCode->getStatus(), $newFeatureCode->getStatus());
    }

    public function testScoreCategoryDocument(): void
    {
        $category = new ScoreCategory();
        $category->setPercentageOfGood(70.5);
        $category->setPercentageOfAverage(20.0);
        $category->setPercentageOfBad(9.5);
        $category->setTips('Maintain smooth acceleration');

        $this->assertEquals(70.5, $category->getPercentageOfGood());
        $this->assertEquals(20.0, $category->getPercentageOfAverage());
        $this->assertEquals(9.5, $category->getPercentageOfBad());
        $this->assertEquals('Maintain smooth acceleration', $category->getTips());
        $this->assertEquals(100.0, $category->getTotalPercentage());
        $this->assertTrue($category->isValid());

        // Test array conversion
        $array = $category->toArray();
        $newCategory = ScoreCategory::fromArray($array);
        $this->assertEquals($category->getPercentageOfGood(), $newCategory->getPercentageOfGood());
    }

    public function testSubScoreDataDocument(): void
    {
        $dynamics = new ScoreCategory();
        $dynamics->setPercentageOfGood(75.0)->setPercentageOfAverage(15.0)->setPercentageOfBad(10.0);
        
        $deceleration = new ScoreCategory();
        $deceleration->setPercentageOfGood(80.0)->setPercentageOfAverage(12.0)->setPercentageOfBad(8.0);
        
        $cornering = new ScoreCategory();
        $cornering->setPercentageOfGood(70.0)->setPercentageOfAverage(20.0)->setPercentageOfBad(10.0);

        $subScore = new SubScoreData();
        $subScore->setDynamics($dynamics);
        $subScore->setDeceleration($deceleration);
        $subScore->setCornering($cornering);

        $this->assertSame($dynamics, $subScore->getDynamics());
        $this->assertSame($deceleration, $subScore->getDeceleration());
        $this->assertSame($cornering, $subScore->getCornering());

        // Test array conversion with typo support
        $array = $subScore->toArray();
        $this->assertArrayHasKey('dynamincs', $array); // Note the typo for backward compatibility
        $this->assertArrayHasKey('decelaration', $array); // Note the typo for backward compatibility
        $this->assertArrayHasKey('cornering', $array);

        $newSubScore = SubScoreData::fromArray($array);
        $this->assertEquals($dynamics->getPercentageOfGood(), $newSubScore->getDynamics()->getPercentageOfGood());
    }

    public function testScoreDataDocument(): void
    {
        $subScore = new SubScoreData();
        $dynamics = new ScoreCategory();
        $dynamics->setPercentageOfGood(75.0);
        $subScore->setDynamics($dynamics);

        $scoreData = new ScoreData();
        $scoreData->setValue(85.5);
        $scoreData->setSubScore($subScore);

        $this->assertEquals(85.5, $scoreData->getValue());
        $this->assertSame($subScore, $scoreData->getSubScore());
        $this->assertEquals(86, $scoreData->getValueAsInt());
        $this->assertEquals('85.50', $scoreData->getValueAsString());
        $this->assertTrue($scoreData->isValid());
        $this->assertEquals('excellent', $scoreData->getCategory());
        $this->assertTrue($scoreData->hasSubScore());

        // Test array conversion
        $array = $scoreData->toArray();
        $newScoreData = ScoreData::fromArray($array);
        $this->assertEquals($scoreData->getValue(), $newScoreData->getValue());
    }

    public function testMileageDataDocument(): void
    {
        $mileage = new MileageData();
        $mileage->setValue(15000);
        $mileage->setTimestamp(1640995200);
        $mileage->setUnit('km');

        $this->assertEquals(15000, $mileage->getValue());
        $this->assertEquals(1640995200, $mileage->getTimestamp());
        $this->assertEquals('km', $mileage->getUnit());
        $this->assertInstanceOf(\DateTime::class, $mileage->getDate());
        $this->assertEquals(9320.565, $mileage->getValueInMiles());
        $this->assertEquals(15000.0, $mileage->getValueInKm());

        // Test array conversion
        $array = $mileage->toArray();
        $newMileage = MileageData::fromArray($array);
        $this->assertEquals($mileage->getValue(), $newMileage->getValue());
    }

    public function testVehicleEnhancedFeatureCodes(): void
    {
        $vehicle = new Vehicle();
        
        // Test new embedded feature codes
        $featureCode1 = new FeatureCode();
        $featureCode1->setCode('UBI_PHYD')->setStatus('enable')->setValue('NAE01');
        
        $featureCode2 = new FeatureCode();
        $featureCode2->setCode('VEHICLE_INFO')->setStatus('disable')->setValue('NAK01');

        $vehicle->addFeatureCodeObject($featureCode1);
        $vehicle->addFeatureCodeObject($featureCode2);

        $this->assertCount(2, $vehicle->getFeatureCodes());
        $this->assertTrue($vehicle->isFeatureCodeEnabled('UBI_PHYD'));
        $this->assertFalse($vehicle->isFeatureCodeEnabled('VEHICLE_INFO'));
        
        $enabledCodes = $vehicle->getEnabledFeatureCodes();
        $this->assertCount(1, $enabledCodes);
        $this->assertEquals('UBI_PHYD', $enabledCodes[0]->getCode());

        // Test backward compatibility sync
        $vehicle->syncFeatureCodesToArray();
        $featureCodeArray = $vehicle->getFeatureCode();
        $this->assertCount(2, $featureCodeArray);
        $this->assertEquals('UBI_PHYD', $featureCodeArray[0]['code']);
    }

    public function testDrivingScoreEnhancedScores(): void
    {
        $drivingScore = new DrivingScore();
        
        // Test new embedded score data
        $overallScore = new ScoreData();
        $overallScore->setValue(85.5);
        
        $dailyScore = new ScoreData();
        $dailyScore->setValue(78.2);
        
        $subScore = new SubScoreData();
        $dynamics = new ScoreCategory();
        $dynamics->setPercentageOfGood(75.0)->setTips('Good acceleration');
        $subScore->setDynamics($dynamics);
        $dailyScore->setSubScore($subScore);

        $drivingScore->setOverallScoreData($overallScore);
        $drivingScore->setDailyScoreData($dailyScore);

        $this->assertEquals(85.5, $drivingScore->getOverallScoreData()->getValue());
        $this->assertEquals(78.2, $drivingScore->getDailyScoreData()->getValue());
        $this->assertTrue($drivingScore->hasDetailedBreakdown());
        $this->assertEquals('excellent', $drivingScore->getDrivingQuality());

        // Test backward compatibility sync
        $drivingScore->syncScoresToArrays();
        $overallArray = $drivingScore->getOverallScore();
        $dailyArray = $drivingScore->getDailyScore();
        
        $this->assertEquals(85.5, $overallArray['value']);
        $this->assertEquals(78.2, $dailyArray['value']);
        $this->assertArrayHasKey('subScore', $dailyArray);
    }
}
