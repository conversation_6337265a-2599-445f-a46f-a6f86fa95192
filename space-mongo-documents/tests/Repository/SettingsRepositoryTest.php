<?php

namespace Space\MongoDocuments\Tests\Repository;

use Doctrine\ODM\MongoDB\DocumentManager;
use Doctrine\ODM\MongoDB\Query\Builder;
use Doctrine\ODM\MongoDB\Query\Expr;
use Doctrine\ODM\MongoDB\Query\Query;
use PHPUnit\Framework\TestCase;
use Space\MongoDocuments\Document\Settings;
use Space\MongoDocuments\Repository\SettingsRepository;

class SettingsRepositoryTest extends TestCase
{
    private SettingsRepository $repository;
    private DocumentManager $documentManager;

    protected function setUp(): void
    {
        $this->documentManager = $this->createMock(DocumentManager::class);
        $this->repository = new SettingsRepository($this->documentManager, null);
    }

    public function testFindByType(): void
    {
        $type = 'configuration';
        $expectedSettings = [new Settings(), new Settings()];

        // Mock the repository's findBy method
        $repository = $this->getMockBuilder(SettingsRepository::class)
            ->setConstructorArgs([$this->documentManager, null])
            ->onlyMethods(['findBy'])
            ->getMock();

        $repository
            ->expects($this->once())
            ->method('findBy')
            ->with(['type' => $type])
            ->willReturn($expectedSettings);

        $result = $repository->findByType($type);

        $this->assertEquals($expectedSettings, $result);
    }

    public function testFindByTypeBrandAndCountry(): void
    {
        $type = 'config';
        $brand = 'DS';
        $country = 'FR';
        $expectedSettings = new Settings();

        // Mock the repository's findOneBy method
        $repository = $this->getMockBuilder(SettingsRepository::class)
            ->setConstructorArgs([$this->documentManager, null])
            ->onlyMethods(['findOneBy'])
            ->getMock();

        $repository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'type' => $type,
                'brand' => $brand,
                'country' => $country
            ])
            ->willReturn($expectedSettings);

        $result = $repository->findByTypeBrandAndCountry($type, $brand, $country);

        $this->assertEquals($expectedSettings, $result);
    }

    public function testFindByTypeAndBrand(): void
    {
        $type = 'config';
        $brand = 'DS';
        $expectedSettings = [new Settings()];

        // Mock the repository's findBy method
        $repository = $this->getMockBuilder(SettingsRepository::class)
            ->setConstructorArgs([$this->documentManager, null])
            ->onlyMethods(['findBy'])
            ->getMock();

        $repository
            ->expects($this->once())
            ->method('findBy')
            ->with([
                'type' => $type,
                'brand' => $brand
            ])
            ->willReturn($expectedSettings);

        $result = $repository->findByTypeAndBrand($type, $brand);

        $this->assertEquals($expectedSettings, $result);
    }

    public function testFindByBrandAndSource(): void
    {
        $brand = 'DS';
        $source = 'APP';
        $expectedSettings = [new Settings()];

        // Mock the repository's findBy method
        $repository = $this->getMockBuilder(SettingsRepository::class)
            ->setConstructorArgs([$this->documentManager, null])
            ->onlyMethods(['findBy'])
            ->getMock();

        $repository
            ->expects($this->once())
            ->method('findBy')
            ->with([
                'brand' => $brand,
                'source' => $source
            ])
            ->willReturn($expectedSettings);

        $result = $repository->findByBrandAndSource($brand, $source);

        $this->assertEquals($expectedSettings, $result);
    }

    public function testFindByComplexFilterBasicFields(): void
    {
        $filter = [
            'brand' => 'DS',
            'source' => 'APP',
            'culture' => 'fr-FR'
        ];

        $expectedSettings = new Settings();
        $queryBuilder = $this->createMock(Builder::class);
        $query = $this->createMock(Query::class);

        // Mock the query builder chain
        $queryBuilder->expects($this->exactly(3))
            ->method('field')
            ->withConsecutive(['brand'], ['source'], ['culture'])
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(3))
            ->method('equals')
            ->withConsecutive(['DS'], ['APP'], ['fr-FR'])
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $query->expects($this->once())
            ->method('getSingleResult')
            ->willReturn($expectedSettings);

        // Mock the repository's createQueryBuilder method
        $repository = $this->getMockBuilder(SettingsRepository::class)
            ->setConstructorArgs([$this->documentManager, null])
            ->onlyMethods(['createQueryBuilder'])
            ->getMock();

        $repository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($queryBuilder);

        $result = $repository->findByComplexFilter($filter);

        $this->assertEquals($expectedSettings, $result);
    }
}
